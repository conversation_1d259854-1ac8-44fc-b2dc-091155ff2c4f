import requests
import json
import os
from datetime import datetime

# Suas credenciais do Zoho
CLIENT_ID = "1000.668VEFGIXESR0SFQF75XJA5F2F2ODO"
CLIENT_SECRET = "a39c8bd608c1dd61757702d6555049c66e4f39e930"
REDIRECT_URI = "https://drip.quarktools.com.br"

# Arquivo para salvar o refresh token
REFRESH_TOKEN_FILE = "refresh_token.txt"
ACCESS_TOKEN_FILE = "access_token.txt"

def gerar_url_autorizacao():
    """
    Gera a URL para autorização inicial (só precisa fazer uma vez)
    """
    scope = "Desk.tickets.READ,Desk.search.READ,Desk.basic.READ"
    url = f"https://accounts.zoho.com/oauth/v2/auth?scope={scope}&client_id={CLIENT_ID}&response_type=code&access_type=offline&redirect_uri={REDIRECT_URI}"
    
    print("=" * 80)
    print("PRIMEIRA VEZ - AUTORIZAÇÃO INICIAL")
    print("=" * 80)
    print("1. Acesse a URL abaixo no seu navegador:")
    print(f"\n{url}\n")
    print("2. Faça login na sua conta Zoho")
    print("3. Autorize o aplicativo")
    print("4. Copie o código de autorização da URL de redirecionamento")
    print("5. Execute: python renovar_token.py --codigo SEU_CODIGO_AQUI")
    print("=" * 80)

def obter_tokens_iniciais(codigo_autorizacao):
    """
    Troca o código de autorização por tokens de acesso e refresh
    """
    url = "https://accounts.zoho.com/oauth/v2/token"
    
    data = {
        "code": codigo_autorizacao,
        "client_id": CLIENT_ID,
        "client_secret": CLIENT_SECRET,
        "redirect_uri": REDIRECT_URI,
        "grant_type": "authorization_code"
    }
    
    try:
        response = requests.post(url, data=data)
        
        if response.status_code == 200:
            tokens = response.json()
            
            # Salvar o refresh token (nunca expira)
            with open(REFRESH_TOKEN_FILE, "w") as f:
                f.write(tokens["refresh_token"])
            
            # Salvar o access token
            with open(ACCESS_TOKEN_FILE, "w") as f:
                f.write(tokens["access_token"])
            
            print("[OK] Tokens obtidos com sucesso!")
            print(f"Access Token: {tokens['access_token']}")
            print(f"Refresh Token salvo em: {REFRESH_TOKEN_FILE}")
            print(f"Access Token salvo em: {ACCESS_TOKEN_FILE}")
            print(f"Expira em: {tokens.get('expires_in', 'N/A')} segundos")
            
            # Atualizar o app.py automaticamente
            atualizar_app_py(tokens["access_token"])
            
            return tokens
        else:
            print(f"[ERRO] Erro ao obter tokens: {response.status_code}")
            print(f"Resposta: {response.text}")
            return None

    except Exception as e:
        print(f"[ERRO] Erro na requisição: {e}")
        return None

def renovar_access_token():
    """
    Renova o access token usando o refresh token
    """
    if not os.path.exists(REFRESH_TOKEN_FILE):
        print("[ERRO] Refresh token não encontrado. Execute a autorização inicial primeiro.")
        gerar_url_autorizacao()
        return None
    
    with open(REFRESH_TOKEN_FILE, "r") as f:
        refresh_token = f.read().strip()
    
    url = "https://accounts.zoho.com/oauth/v2/token"
    
    data = {
        "refresh_token": refresh_token,
        "client_id": CLIENT_ID,
        "client_secret": CLIENT_SECRET,
        "grant_type": "refresh_token"
    }
    
    try:
        response = requests.post(url, data=data)
        
        if response.status_code == 200:
            tokens = response.json()
            
            # Salvar o novo access token
            with open(ACCESS_TOKEN_FILE, "w") as f:
                f.write(tokens["access_token"])
            
            print("[OK] Access token renovado com sucesso!")
            print(f"Novo Access Token: {tokens['access_token']}")
            print(f"Expira em: {tokens.get('expires_in', 3600)} segundos")
            
            # Atualizar o app.py automaticamente
            atualizar_app_py(tokens["access_token"])
            
            return tokens["access_token"]
        else:
            print(f"[ERRO] Erro ao renovar token: {response.status_code}")
            print(f"Resposta: {response.text}")
            return None

    except Exception as e:
        print(f"[ERRO] Erro na requisição: {e}")
        return None

def atualizar_app_py(novo_token):
    """
    Atualiza automaticamente o token no arquivo app.py
    """
    try:
        # Ler o arquivo app.py
        with open("app.py", "r", encoding="utf-8") as f:
            conteudo = f.read()

        # Encontrar e substituir o token
        import re
        padrao = r'ACCESS_TOKEN = "[^"]*"'
        novo_conteudo = re.sub(padrao, f'ACCESS_TOKEN = "{novo_token}"', conteudo)

        # Salvar o arquivo atualizado
        with open("app.py", "w", encoding="utf-8") as f:
            f.write(novo_conteudo)

        print("[OK] Arquivo app.py atualizado automaticamente!")

    except Exception as e:
        print(f"[AVISO] Erro ao atualizar app.py: {e}")
        print("Você precisará atualizar manualmente o token no arquivo app.py")

def atualizar_todos_arquivos(novo_token):
    """
    Atualiza o token em todos os arquivos Python do projeto
    """
    import re
    import glob

    arquivos_para_atualizar = [
        "app.py",
        "app_simples.py",
        "baixar_relatorio_completo.py",
        "diagnosticar_problema.py"
    ]

    padrao = r'ACCESS_TOKEN = "[^"]*"'

    for arquivo in arquivos_para_atualizar:
        try:
            if os.path.exists(arquivo):
                with open(arquivo, "r", encoding="utf-8") as f:
                    conteudo = f.read()

                novo_conteudo = re.sub(padrao, f'ACCESS_TOKEN = "{novo_token}"', conteudo)

                with open(arquivo, "w", encoding="utf-8") as f:
                    f.write(novo_conteudo)

                print(f"[OK] {arquivo} atualizado!")
            else:
                print(f"[SKIP] {arquivo} não encontrado")

        except Exception as e:
            print(f"[ERRO] Erro ao atualizar {arquivo}: {e}")

def verificar_token_valido():
    """
    Verifica se o token atual ainda é válido
    """
    if not os.path.exists(ACCESS_TOKEN_FILE):
        print("[ERRO] Access token não encontrado.")
        return False
    
    with open(ACCESS_TOKEN_FILE, "r") as f:
        access_token = f.read().strip()
    
    # Testar o token fazendo uma requisição simples
    headers = {
        "Authorization": f"Zoho-oauthtoken {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        # Tenta acessar a API do Zoho Desk
        response = requests.get("https://desk.zoho.com/api/v1/tickets?limit=1", headers=headers, timeout=30)

        if response.status_code == 200:
            print("[OK] Token atual ainda é válido!")
            return True
        elif response.status_code == 401:
            print(f"[ERRO] Token inválido ou expirado (Status: {response.status_code})")
            return False
        else:
            print(f"[AVISO] Status inesperado: {response.status_code}")
            print(f"Resposta: {response.text[:200]}...")
            return False

    except Exception as e:
        print(f"[ERRO] Erro ao verificar token: {e}")
        return False

if __name__ == "__main__":
    import sys
    
    print("=" * 80)
    print("RENOVADOR DE TOKEN ZOHO DESK")
    print("=" * 80)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--codigo" and len(sys.argv) > 2:
            # Primeira vez - trocar código por tokens
            codigo = sys.argv[2]
            obter_tokens_iniciais(codigo)
        elif sys.argv[1] == "--url":
            # Gerar URL de autorização
            gerar_url_autorizacao()
        elif sys.argv[1] == "--verificar":
            # Verificar se o token atual é válido
            verificar_token_valido()
        else:
            print("Uso:")
            print("  python renovar_token.py --url                    # Gerar URL de autorização")
            print("  python renovar_token.py --codigo SEU_CODIGO      # Primeira vez (trocar código por tokens)")
            print("  python renovar_token.py --verificar             # Verificar se token atual é válido")
            print("  python renovar_token.py                         # Renovar token usando refresh token")
    else:
        # Renovar token usando refresh token
        print("Verificando token atual...")
        if verificar_token_valido():
            print("Token atual ainda é válido. Não é necessário renovar.")
        else:
            print("Token inválido ou expirado. Renovando...")
            novo_token = renovar_access_token()
            if novo_token:
                print("✅ Token renovado com sucesso!")
                # Atualizar também outros arquivos
                atualizar_todos_arquivos(novo_token)
            else:
                print("❌ Falha ao renovar token. Verifique suas credenciais.")
