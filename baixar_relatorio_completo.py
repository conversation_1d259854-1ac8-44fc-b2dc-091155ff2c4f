#!/usr/bin/env python3
"""
Script de automação completa para baixar relatórios do Zoho Desk
Este script automatiza todo o processo:
1. Verifica se o token é válido
2. Renova o token se necessário
3. Baixa o relatório completo
4. Mostra estatísticas do relatório
"""

import subprocess
import sys
import os
from datetime import datetime
import pandas as pd

# Configuração para resolver problemas de codificação no Windows
if sys.platform.startswith('win'):
    # Força o console a usar UTF-8
    try:
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except:
        # Para versões mais antigas do Python
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
    
    # Define a codepage do console para UTF-8
    os.system('chcp 65001 > nul')

def executar_comando(comando, descricao):
    """
    Executa um comando e mostra o resultado
    """
    print(f"\n{'='*60}")
    print(f"EXECUTANDO: {descricao}")
    print(f"{'='*60}")
    
    try:
        resultado = subprocess.run(comando, shell=True, capture_output=True, text=True)
        
        if resultado.stdout:
            print("SAÍDA:")
            print(resultado.stdout)
        
        if resultado.stderr:
            print("ERROS:")
            print(resultado.stderr)
        
        return resultado.returncode == 0
        
    except Exception as e:
        print(f"[ERRO] Erro ao executar comando: {e}")
        return False

def verificar_arquivos_necessarios():
    """
    Verifica se todos os arquivos necessários existem
    """
    arquivos = ["app.py", "renovar_token.py"]
    
    for arquivo in arquivos:
        if not os.path.exists(arquivo):
            print(f"[ERRO] Arquivo {arquivo} não encontrado!")
            return False
    
    print("[OK] Todos os arquivos necessários encontrados!")
    return True

def mostrar_estatisticas_relatorio():
    """
    Mostra estatísticas do relatório gerado
    """
    arquivo_relatorio = "emails_enviados_historico_completo.csv"
    
    if not os.path.exists(arquivo_relatorio):
        print(f"[ERRO] Arquivo de relatório {arquivo_relatorio} não encontrado!")
        return
    
    try:
        df = pd.read_csv(arquivo_relatorio)
        
        print(f"\n{'='*60}")
        print("ESTATÍSTICAS DO RELATÓRIO")
        print(f"{'='*60}")
        
        print(f"[DADOS] Total de emails: {len(df)}")
        
        if len(df) > 0:
            # Converter a coluna de data
            df['sent_at'] = pd.to_datetime(df['sent_at'])
            
            # Estatísticas por agente
            print(f"\n[AGENTES] Emails por agente:")
            emails_por_agente = df['agent'].value_counts()
            for agente, count in emails_por_agente.items():
                print(f"  • {agente}: {count} emails")
            
            # Período dos dados
            data_inicio = df['sent_at'].min()
            data_fim = df['sent_at'].max()
            print(f"\n[PERIODO] Período dos dados:")
            print(f"  • Início: {data_inicio.strftime('%d/%m/%Y %H:%M:%S')}")
            print(f"  • Fim: {data_fim.strftime('%d/%m/%Y %H:%M:%S')}")
            
            # Estatísticas por mês
            df['mes_ano'] = df['sent_at'].dt.to_period('M')
            emails_por_mes = df['mes_ano'].value_counts().sort_index()
            print(f"\n[MENSAL] Emails por mês:")
            for mes, count in emails_por_mes.items():
                print(f"  • {mes}: {count} emails")
        
        print(f"\n[SUCESSO] Relatório salvo em: {arquivo_relatorio}")
        
    except Exception as e:
        print(f"[ERRO] Erro ao analisar relatório: {e}")

def main():
    """
    Função principal que executa todo o processo
    """
    print("[INICIO] DOWNLOAD COMPLETO DO RELATÓRIO ZOHO DESK")
    print(f"[HORARIO] Horário de início: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    
    # Verificar arquivos necessários
    if not verificar_arquivos_necessarios():
        print("[ERRO] Processo interrompido devido a arquivos faltantes.")
        return
    
    # Passo 1: Verificar e renovar token se necessário
    print("\n[PASSO 1] Verificando token de acesso...")
    if not executar_comando("python renovar_token.py", "Verificar/Renovar Token"):
        print("[AVISO] Problema com o token. Tentando renovar...")
        if not executar_comando("python renovar_token.py", "Forçar Renovação do Token"):
            print("[ERRO] Não foi possível renovar o token. Verifique suas credenciais.")
            print("\nSe esta é a primeira vez, execute:")
            print("  python renovar_token.py --url")
            print("E siga as instruções para autorizar o aplicativo.")
            return
    
    # Passo 2: Baixar o relatório
    print("\n[PASSO 2] Baixando relatório completo...")
    if not executar_comando("python app.py", "Download do Relatório"):
        print("[ERRO] Erro ao baixar o relatório.")
        return
    
    # Passo 3: Mostrar estatísticas
    print("\n[PASSO 3] Analisando relatório...")
    mostrar_estatisticas_relatorio()
    
    print(f"\n[CONCLUIDO] PROCESSO CONCLUÍDO!")
    print(f"[HORARIO] Horário de conclusão: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("\n[PROXIMOS PASSOS]")
    print("1. Copie o arquivo 'emails_enviados_historico_completo.csv' para o diretório do dashboard")
    print("2. Atualize o nome do arquivo no dashboard.py se necessário")
    print("3. Execute o dashboard para visualizar os dados")

if __name__ == "__main__":
    # Verificar se pandas está instalado
    try:
        import pandas as pd
    except ImportError:
        print("[AVISO] Pandas não está instalado. Instalando...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pandas"], check=True)
        import pandas as pd
    
    main()
