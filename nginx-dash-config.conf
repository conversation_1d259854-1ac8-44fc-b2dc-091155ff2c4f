# Redirecionamento HTTP para HTTPS
server {
    listen 80;
    server_name suporte.quarktools.com.br;
    return 301 https://$server_name$request_uri;
}

# Configuração HTTPS
server {
    listen 443 ssl http2;
    server_name suporte.quarktools.com.br;

    ssl_certificate /etc/letsencrypt/live/suporte.quarktools.com.br/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/suporte.quarktools.com.br/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # Configurações gerais do proxy
    proxy_http_version 1.1;
    proxy_cache_bypass $http_upgrade;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;

    # Aumentar timeouts para evitar desconexões
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # Configuração do Streamlit
    location / {
        proxy_pass http://localhost:8501;
        proxy_set_header Connection "";
        proxy_buffering off;
    }

    # Configuração para WebSocket do Streamlit
    location /stream {
        proxy_pass http://localhost:8501/stream;
        proxy_http_version 1.1;
        proxy_set_header Connection "upgrade";
        proxy_set_header Upgrade $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Configuração para assets estáticos do Streamlit
    location /_stcore {
        proxy_pass http://localhost:8501/_stcore;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }

    # Configuração para vendor files do Streamlit
    location /vendor {
        proxy_pass http://localhost:8501/vendor;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }

    # Configuração para a aplicação Dash
    location /report/ {
        proxy_pass http://localhost:8050/report/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering off;
    }

    # Configuração para assets estáticos do Dash
    location /report/_dash-assets/ {
        proxy_pass http://localhost:8050/report/_dash-assets/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # Configuração para componentes do Dash
    location /report/_dash-component-suites/ {
        proxy_pass http://localhost:8050/report/_dash-component-suites/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # Configuração para layout do Dash
    location /report/_dash-layout {
        proxy_pass http://localhost:8050/report/_dash-layout;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Configuração para dependências do Dash
    location /report/_dash-dependencies {
        proxy_pass http://localhost:8050/report/_dash-dependencies;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Configuração para atualizações do Dash
    location /report/_dash-update-component {
        proxy_pass http://localhost:8050/report/_dash-update-component;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 86400;
    }
}
