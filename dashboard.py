import dash
from dash import dcc, html, Input, Output
import dash_bootstrap_components as dbc
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import numpy as np

# Inicializar a aplicação Dash
app = dash.Dash(
    __name__,
    external_stylesheets=[dbc.themes.BOOTSTRAP],
    routes_pathname_prefix='/report/',
    suppress_callback_exceptions=True  # Suprimir exceções de callback para componentes gerados dinamicamente
)
server = app.server

# Configurar o título da página
app.title = "Dashboard de Produtividade - Zoho Desk"

# Adicionar CSS personalizado
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        {%css%}
        <style>
            /* Estilos para garantir que os dropdowns sejam exibidos corretamente */
            .Select-menu-outer {
                z-index: 9999 !important;
            }

            /* Espaçamento para os filtros */
            .mt-3 {
                margin-top: 15px;
            }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>
'''

# Mapeamento de nomes fictícios para nomes reais
AGENT_MAPPING = {
    'Jade Fierro': 'Dayane Garcia',
    'Isabella  Carter': 'Renato Ponce',
    'Emma Collins': 'André Pereira',
    'Julia Foster': 'Jamille Barbosa',
    'Sofia Bianchi': 'Edyliana',
    'DripSlum': 'DripSlum'
}

# Função para carregar os dados
def load_data():
    try:
        # Tenta carregar o arquivo CSV principal
        df = pd.read_csv('emails_enviados_historico_completo.csv')
    except:
        # Se nenhum arquivo for encontrado, cria um DataFrame vazio
        df = pd.DataFrame(columns=['ticket_number', 'subject', 'agent', 'sent_at'])

    # Converte a coluna sent_at para datetime
    if not df.empty and 'sent_at' in df.columns:
        df['sent_at'] = pd.to_datetime(df['sent_at'])

        # Ajusta o fuso horário (subtrai 3 horas)
        df['sent_at'] = df['sent_at'] - pd.Timedelta(hours=3)

        # Mapeia os nomes dos agentes para os nomes reais
        df['agent_original'] = df['agent']
        df['agent'] = df['agent'].map(AGENT_MAPPING).fillna(df['agent'])

        df['date'] = df['sent_at'].dt.date
        df['hour'] = df['sent_at'].dt.hour
        df['day_of_week'] = df['sent_at'].dt.day_name()
        df['day_of_week_num'] = df['sent_at'].dt.dayofweek

    return df

def calcular_tempo_trabalho(df_agente):
    """
    Calcula o tempo total de trabalho para um agente baseado na nova lógica:
    1. Criar blocos contínuos de trabalho (gap máximo de 1h 15min)
    2. Aplicar tempo mínimo de 1 hora por bloco
    3. Somar todas as durações dos blocos
    """
    if df_agente.empty:
        return timedelta(0), []

    # Ordenar por data/hora
    df_sorted = df_agente.sort_values('sent_at').copy()

    tempo_total = timedelta(0)
    detalhes_blocos = []

    # Agrupar por data para calcular o tempo de trabalho por dia
    for date, group in df_sorted.groupby('date'):
        emails_do_dia = group.sort_values('sent_at')

        if len(emails_do_dia) == 0:
            continue

        # Criar blocos contínuos de trabalho
        blocos = []
        bloco_atual = [emails_do_dia.iloc[0]['sent_at']]

        for i in range(1, len(emails_do_dia)):
            email_anterior = emails_do_dia.iloc[i-1]['sent_at']
            email_atual = emails_do_dia.iloc[i]['sent_at']

            # Calcular gap entre emails
            gap = email_atual - email_anterior

            if gap <= timedelta(hours=1, minutes=15):
                # Continua o bloco atual
                bloco_atual.append(email_atual)
            else:
                # Fecha o bloco anterior e inicia um novo
                blocos.append(bloco_atual)
                bloco_atual = [email_atual]

        # Adicionar o último bloco
        if bloco_atual:
            blocos.append(bloco_atual)

        # Calcular duração de cada bloco
        for bloco in blocos:
            if len(bloco) == 1:
                # Bloco com apenas 1 email = tempo mínimo de 1 hora
                duracao_aplicada = timedelta(hours=1)
                duracao_real = timedelta(0)
            else:
                # Calcular duração real do bloco
                inicio_bloco = min(bloco)
                fim_bloco = max(bloco)
                duracao_real = fim_bloco - inicio_bloco

                # Aplicar tempo mínimo de 1 hora
                duracao_aplicada = max(duracao_real, timedelta(hours=1))

            tempo_total += duracao_aplicada

            # Guardar detalhes para debug/relatório
            detalhes_blocos.append({
                'data': date,
                'inicio': min(bloco),
                'fim': max(bloco),
                'emails_no_bloco': len(bloco),
                'duracao_real': duracao_real,
                'duracao_aplicada': duracao_aplicada
            })

    return tempo_total, detalhes_blocos

def formatar_tempo_trabalho(tempo_delta):
    """
    Formata um timedelta em formato legível (Xh Ym)
    """
    if tempo_delta.total_seconds() == 0:
        return "0h 0m"

    total_seconds = int(tempo_delta.total_seconds())
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60

    return f"{hours}h {minutes}m"

def calcular_estatisticas_trabalho(filtered_df):
    """
    Calcula estatísticas de tempo de trabalho para todos os agentes usando a nova lógica
    """
    if filtered_df.empty:
        return pd.DataFrame(), {}

    estatisticas = []
    detalhes_todos_agentes = {}

    for agente in filtered_df['agent'].unique():
        df_agente = filtered_df[filtered_df['agent'] == agente]

        # Calcular tempo total de trabalho com a nova lógica
        tempo_total, detalhes_blocos = calcular_tempo_trabalho(df_agente)

        # Calcular número de dias únicos trabalhados
        dias_trabalhados = df_agente['date'].nunique()

        # Calcular número de blocos de trabalho
        total_blocos = len(detalhes_blocos)

        # Calcular tempo médio por dia
        if dias_trabalhados > 0:
            tempo_medio_dia = tempo_total / dias_trabalhados
        else:
            tempo_medio_dia = timedelta(0)

        # Calcular tempo médio por bloco
        if total_blocos > 0:
            tempo_medio_bloco = tempo_total / total_blocos
        else:
            tempo_medio_bloco = timedelta(0)

        # Total de emails
        total_emails = len(df_agente)

        estatisticas.append({
            'agente': agente,
            'tempo_total': tempo_total,
            'tempo_total_str': formatar_tempo_trabalho(tempo_total),
            'dias_trabalhados': dias_trabalhados,
            'total_blocos': total_blocos,
            'tempo_medio_dia': tempo_medio_dia,
            'tempo_medio_dia_str': formatar_tempo_trabalho(tempo_medio_dia),
            'tempo_medio_bloco_str': formatar_tempo_trabalho(tempo_medio_bloco),
            'total_emails': total_emails,
            'tempo_total_horas': tempo_total.total_seconds() / 3600  # Para gráficos
        })

        # Guardar detalhes dos blocos para cada agente
        detalhes_todos_agentes[agente] = detalhes_blocos

    return pd.DataFrame(estatisticas), detalhes_todos_agentes

def calcular_detalhamento_diario(filtered_df):
    """
    Calcula o detalhamento diário de trabalho por agente
    """
    if filtered_df.empty:
        return pd.DataFrame()

    detalhamento_diario = []

    for agente in filtered_df['agent'].unique():
        df_agente = filtered_df[filtered_df['agent'] == agente]

        # Agrupar por data
        for date, group in df_agente.groupby('date'):
            emails_do_dia = group.sort_values('sent_at')

            # Calcular tempo de trabalho do dia
            tempo_dia, blocos_dia = calcular_tempo_trabalho(emails_do_dia)

            # Calcular horário de início e fim
            if not emails_do_dia.empty:
                primeiro_email = emails_do_dia['sent_at'].min()
                ultimo_email = emails_do_dia['sent_at'].max()

                # Formatar horários
                horario_inicio = primeiro_email.strftime('%H:%M')
                horario_fim = ultimo_email.strftime('%H:%M')

                # Contar emails/tickets
                total_emails_dia = len(emails_do_dia)
                total_tickets_dia = emails_do_dia['ticket_number'].nunique()

                # Criar descrição dos blocos
                if blocos_dia:
                    blocos_desc = []
                    for bloco in blocos_dia:
                        inicio_bloco = bloco['inicio'].strftime('%H:%M')
                        fim_bloco = bloco['fim'].strftime('%H:%M')
                        duracao_bloco = formatar_tempo_trabalho(bloco['duracao_aplicada'])
                        blocos_desc.append(f"{inicio_bloco}-{fim_bloco} ({duracao_bloco})")

                    detalhes_blocos = " | ".join(blocos_desc)
                else:
                    detalhes_blocos = f"{horario_inicio}-{horario_fim}"

                detalhamento_diario.append({
                    'agente': agente,
                    'data': date,
                    'data_str': date.strftime('%d/%m/%Y'),
                    'horario_inicio': horario_inicio,
                    'horario_fim': horario_fim,
                    'tempo_trabalhado': tempo_dia,
                    'tempo_trabalhado_str': formatar_tempo_trabalho(tempo_dia),
                    'total_emails': total_emails_dia,
                    'total_tickets': total_tickets_dia,
                    'detalhes_blocos': detalhes_blocos,
                    'resumo': f"{formatar_tempo_trabalho(tempo_dia)} - {total_tickets_dia} tickets - {detalhes_blocos}"
                })

    return pd.DataFrame(detalhamento_diario)

# Carregar os dados
df = load_data()

# Layout da aplicação
app.layout = dbc.Container([
    dbc.Row([
        dbc.Col([
            html.H1("Dashboard de Produtividade - Zoho Desk", className="text-center my-4"),
            html.P("Análise de emails enviados em abril de 2025", className="text-center"),
            dbc.Button("Atualizar Dados", id="refresh-button", color="primary", className="mb-3"),
        ], width=12)
    ]),

    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Filtros"),
                dbc.CardBody([
                    html.P("Selecione o Agente:"),
                    dcc.Dropdown(
                        id='agent-filter',
                        options=[{'label': 'Todos', 'value': 'all'}] +
                                [{'label': agent, 'value': agent} for agent in sorted(df['agent'].unique())] if not df.empty else [],
                        value='all',
                        clearable=False
                    ),
                    html.P("Selecione o Período:", className="mt-3"),
                    dcc.DatePickerRange(
                        id='date-range',
                        start_date=df['sent_at'].min().date() if not df.empty else None,
                        end_date=df['sent_at'].max().date() if not df.empty else None,
                        display_format='DD/MM/YYYY',
                        minimum_nights=0,  # Permite selecionar o mesmo dia para início e fim
                        first_day_of_week=0,  # Semana começa no domingo (0) ou segunda (1)
                        month_format='MMMM YYYY',  # Formato do mês no calendário
                        clearable=True,  # Permite limpar a seleção
                        with_portal=True,  # Abre o calendário em um portal (melhor visualização)
                        updatemode='singledate'  # Atualiza ao selecionar uma única data
                    ),
                ])
            ], className="mb-4")
        ], width=12)
    ]),

    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Total de E-mails Respondidos por Agente"),
                dbc.CardBody([
                    dcc.Graph(id='emails-by-agent')
                ])
            ])
        ], width=6),
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Respostas por Hora do Dia"),
                dbc.CardBody([
                    dcc.Graph(id='emails-by-hour')
                ])
            ])
        ], width=6)
    ], className="mb-4"),

    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Heatmap de Horários Ativos por Agente"),
                dbc.CardBody([
                    dcc.Graph(id='heatmap-by-agent-hour')
                ])
            ])
        ], width=12)
    ], className="mb-4"),

    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Respostas por Dia da Semana"),
                dbc.CardBody([
                    dcc.Graph(id='emails-by-day')
                ])
            ])
        ], width=6),
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Hora da Primeira e Última Resposta por Agente"),
                dbc.CardBody([
                    dcc.Graph(id='first-last-response')
                ])
            ])
        ], width=6)
    ], className="mb-4"),

    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Tempo Total de Trabalho por Agente"),
                dbc.CardBody([
                    dcc.Graph(id='tempo-trabalho-graph')
                ])
            ])
        ], width=6),
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Estatísticas de Tempo de Trabalho"),
                dbc.CardBody([
                    html.Div(id='estatisticas-tempo')
                ])
            ])
        ], width=6)
    ], className="mb-4"),

    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Detalhamento Diário por Agente"),
                dbc.CardBody([
                    html.Div(id='detalhamento-diario')
                ])
            ])
        ], width=12)
    ], className="mb-4"),

    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Detalhes dos E-mails"),
                dbc.CardBody([
                    html.Div(id='email-details'),
                    # Adicionar o componente de paginação vazio no layout inicial
                    html.Div([
                        html.Div([
                            html.Span("Total de 0 emails no período selecionado. ", className="mr-2"),
                            dcc.Dropdown(
                                id='page-selector',
                                options=[{'label': 'Página 1 de 1', 'value': 0}],
                                value=0,
                                clearable=False,
                                style={'width': '200px', 'display': 'inline-block'}
                            )
                        ], className="d-flex align-items-center mb-2"),
                        html.Div(id='pagination-content')
                    ], style={'display': 'none'})
                ])
            ])
        ], width=12)
    ])
], fluid=True)

# Callbacks para atualizar os gráficos

@app.callback(
    [Output('emails-by-agent', 'figure'),
     Output('emails-by-hour', 'figure'),
     Output('heatmap-by-agent-hour', 'figure'),
     Output('emails-by-day', 'figure'),
     Output('first-last-response', 'figure'),
     Output('tempo-trabalho-graph', 'figure'),
     Output('estatisticas-tempo', 'children'),
     Output('detalhamento-diario', 'children'),
     Output('email-details', 'children')],
    [Input('agent-filter', 'value'),
     Input('date-range', 'start_date'),
     Input('date-range', 'end_date'),
     Input('refresh-button', 'n_clicks')]
)
def update_graphs(selected_agent, start_date, end_date, n_clicks):
    # Recarregar os dados a cada atualização
    df = load_data()

    if df.empty:
        empty_fig = px.bar(title="Sem dados disponíveis")
        empty_table = html.P("Nenhum dado disponível")
        empty_stats = html.P("Nenhum dado disponível")
        empty_daily = html.P("Nenhum dado disponível")
        return empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_stats, empty_daily, empty_table

    # Filtrar por agente
    if selected_agent != 'all':
        filtered_df = df[df['agent'] == selected_agent]
    else:
        filtered_df = df

    # Filtrar por data
    if start_date and end_date:
        start_date = pd.to_datetime(start_date).date()
        end_date = pd.to_datetime(end_date).date()
        filtered_df = filtered_df[(filtered_df['sent_at'].dt.date >= start_date) &
                                 (filtered_df['sent_at'].dt.date <= end_date)]

    if filtered_df.empty:
        empty_fig = px.bar(title="Sem dados disponíveis para os filtros selecionados")
        empty_table = html.P("Nenhum dado disponível para os filtros selecionados")
        empty_stats = html.P("Nenhum dado disponível para os filtros selecionados")
        empty_daily = html.P("Nenhum dado disponível para os filtros selecionados")
        return empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_fig, empty_stats, empty_daily, empty_table

    # 1. Total de e-mails respondidos por agente
    emails_by_agent = filtered_df.groupby('agent').size().reset_index(name='count')
    emails_by_agent = emails_by_agent.sort_values('count', ascending=True)
    fig1 = px.bar(emails_by_agent, y='agent', x='count', orientation='h',
                 title="Total de E-mails Respondidos por Agente",
                 labels={'count': 'Número de E-mails', 'agent': 'Agente'},
                 color='count', color_continuous_scale='Viridis')

    # 2. Respostas por hora do dia
    emails_by_hour = filtered_df.groupby(['hour', 'agent']).size().reset_index(name='count')
    fig2 = px.bar(emails_by_hour, x='hour', y='count', color='agent', barmode='stack',
                 title="Respostas por Hora do Dia",
                 labels={'count': 'Número de E-mails', 'hour': 'Hora do Dia'},
                 category_orders={"hour": list(range(24))})

    # 3. Heatmap de horários ativos por agente
    heatmap_data = filtered_df.groupby(['agent', 'hour']).size().reset_index(name='count')
    heatmap_pivot = heatmap_data.pivot(index='agent', columns='hour', values='count').fillna(0)

    # Garantir que todas as horas estejam representadas
    for hour in range(24):
        if hour not in heatmap_pivot.columns:
            heatmap_pivot[hour] = 0
    heatmap_pivot = heatmap_pivot.reindex(columns=sorted(heatmap_pivot.columns))

    fig3 = px.imshow(heatmap_pivot,
                    labels=dict(x="Hora do Dia", y="Agente", color="Número de E-mails"),
                    x=list(range(24)),
                    color_continuous_scale='Viridis',
                    title="Heatmap de Horários Ativos por Agente")

    # 4. Respostas por dia da semana
    day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    day_order_pt = ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo']
    day_map = dict(zip(day_order, day_order_pt))

    emails_by_day = filtered_df.groupby(['day_of_week', 'day_of_week_num']).size().reset_index(name='count')
    emails_by_day['day_pt'] = emails_by_day['day_of_week'].map(day_map)
    emails_by_day = emails_by_day.sort_values('day_of_week_num')

    fig4 = px.bar(emails_by_day, x='day_pt', y='count',
                 title="Respostas por Dia da Semana",
                 labels={'count': 'Número de E-mails', 'day_pt': 'Dia da Semana'},
                 color='count', color_continuous_scale='Viridis')

    # 5. Hora da primeira e última resposta por agente
    first_last = filtered_df.groupby(['agent', 'date']).agg(
        first_response=('sent_at', 'min'),
        last_response=('sent_at', 'max')
    ).reset_index()

    first_last['first_hour'] = first_last['first_response'].dt.hour + first_last['first_response'].dt.minute/60
    first_last['last_hour'] = first_last['last_response'].dt.hour + first_last['last_response'].dt.minute/60

    avg_first_last = first_last.groupby('agent').agg(
        avg_first=('first_hour', 'mean'),
        avg_last=('last_hour', 'mean')
    ).reset_index()

    fig5 = go.Figure()

    for i, row in avg_first_last.iterrows():
        fig5.add_trace(go.Bar(
            name=row['agent'],
            y=[row['agent']],
            x=[row['avg_last'] - row['avg_first']],
            orientation='h',
            base=row['avg_first'],
            marker=dict(color='rgba(58, 71, 80, 0.6)')
        ))

    fig5.update_layout(
        title="Hora Média da Primeira e Última Resposta por Agente",
        xaxis_title="Hora do Dia",
        yaxis_title="Agente",
        barmode='stack',
        xaxis=dict(
            tickmode='array',
            tickvals=list(range(25)),
            ticktext=[f"{h}:00" for h in range(25)]
        )
    )

    # 6. Cálculo do tempo de trabalho com nova lógica
    estatisticas_trabalho, detalhes_blocos = calcular_estatisticas_trabalho(filtered_df)

    if not estatisticas_trabalho.empty:
        # Gráfico de tempo total de trabalho por agente
        fig6 = px.bar(
            estatisticas_trabalho.sort_values('tempo_total_horas', ascending=True),
            y='agente',
            x='tempo_total_horas',
            orientation='h',
            title="Tempo Total de Trabalho por Agente (Gap máx: 1h + Mínimo 1h/bloco)",
            labels={'tempo_total_horas': 'Horas Trabalhadas', 'agente': 'Agente'},
            color='tempo_total_horas',
            color_continuous_scale='Viridis',
            text='tempo_total_str'
        )
        fig6.update_traces(textposition='outside')
        fig6.update_layout(
            xaxis_title="Horas Trabalhadas",
            yaxis_title="Agente",
            showlegend=False
        )

        # Tabela de estatísticas expandida
        stats_table = dbc.Table.from_dataframe(
            estatisticas_trabalho[['agente', 'tempo_total_str', 'dias_trabalhados',
                                 'total_blocos', 'tempo_medio_dia_str',
                                 'tempo_medio_bloco_str', 'total_emails']].rename(columns={
                'agente': 'Agente',
                'tempo_total_str': 'Tempo Total',
                'dias_trabalhados': 'Dias Ativos',
                'total_blocos': 'Blocos de Trabalho',
                'tempo_medio_dia_str': 'Média/Dia',
                'tempo_medio_bloco_str': 'Média/Bloco',
                'total_emails': 'E-mails'
            }),
            striped=True,
            bordered=True,
            hover=True,
            responsive=True,
            size='sm'
        )

        stats_section = [
            html.H6("Resumo por Agente (Nova Metodologia)", className="mb-3"),
            stats_table,
            html.Hr(),
            html.Div([
                html.H6("Metodologia de Cálculo:", className="mb-2"),
                html.Ul([
                    html.Li("Blocos contínuos: Gap máximo de 1h entre emails"),
                    html.Li("Tempo mínimo: 1 hora por bloco de trabalho"),
                    html.Li("Duração real vs. aplicada: Se bloco < 1h, conta como 1h"),
                    html.Li("Total: Soma de todos os blocos com mínimo aplicado")
                ], className="mb-2"),
                html.P([
                    html.Strong("Exemplos práticos:")
                ], className="mb-2"),
                html.Ul([
                    html.Li("Emails às 09:00, 09:15, 09:30 = 1 bloco de 30min → conta 1h (mínimo aplicado)"),
                    html.Li("Emails às 09:00, 10:30 = 1 bloco de 90min → conta 90min (duração real)"),
                    html.Li("Emails às 09:00, 10:30, 12:00 = 2 blocos separados (gap de 1h30min > 1h)"),
                    html.Li("Emails às 09:00, 09:45, 10:30 = 1 bloco de 90min (gaps ≤ 1h)")
                ], className="text-muted small"),
                html.P([
                    html.Strong("Regra do Gap: "),
                    "Se a diferença entre dois emails consecutivos for maior que 1h, "
                    "considera-se que houve uma pausa (almoço, intervalo, etc.) e inicia-se um novo bloco de trabalho."
                ], className="text-muted small")
            ])
        ]
    else:
        fig6 = px.bar(title="Sem dados suficientes para calcular tempo de trabalho")
        stats_section = html.P("Sem dados suficientes para calcular estatísticas de tempo")

    # 7. Detalhamento diário por agente
    detalhamento_diario = calcular_detalhamento_diario(filtered_df)

    if not detalhamento_diario.empty:
        # Criar seções por agente
        daily_sections = []

        for agente in detalhamento_diario['agente'].unique():
            df_agente_diario = detalhamento_diario[detalhamento_diario['agente'] == agente].sort_values('data')

            # Período do agente
            data_inicio = df_agente_diario['data_str'].iloc[0]
            data_fim = df_agente_diario['data_str'].iloc[-1]

            if data_inicio == data_fim:
                periodo_str = data_inicio
            else:
                periodo_str = f"{data_inicio} ~ {data_fim}"

            # Criar tabela para o agente
            agente_table = dbc.Table.from_dataframe(
                df_agente_diario[['data_str', 'resumo']].rename(columns={
                    'data_str': 'Data',
                    'resumo': 'Horários e Atividade'
                }),
                striped=True,
                bordered=True,
                hover=True,
                responsive=True,
                size='sm'
            )

            # Calcular totais do agente no período
            tempo_total_agente = df_agente_diario['tempo_trabalhado'].sum()
            total_tickets_agente = df_agente_diario['total_tickets'].sum()
            total_emails_agente = df_agente_diario['total_emails'].sum()
            dias_trabalhados = len(df_agente_diario)

            daily_sections.append(
                html.Div([
                    html.H5(f"{agente} - {periodo_str}", className="mb-3"),
                    html.P([
                        html.Strong(f"Resumo: "),
                        f"{formatar_tempo_trabalho(tempo_total_agente)} em {dias_trabalhados} dias | "
                        f"{total_tickets_agente} tickets | {total_emails_agente} emails"
                    ], className="text-muted mb-3"),
                    agente_table,
                    html.Hr()
                ], className="mb-4")
            )

        daily_detail_section = daily_sections
    else:
        daily_detail_section = html.P("Nenhum dado disponível para detalhamento diário")

    # 8. Tabela de detalhes dos e-mails com paginação
    # Preparar os dados para a tabela
    table_df = filtered_df.sort_values('sent_at', ascending=False).copy()
    table_df['sent_at_str'] = table_df['sent_at'].dt.strftime('%d/%m/%Y %H:%M:%S')

    # Selecionar as colunas para a tabela
    display_columns = ['ticket_number', 'subject', 'agent', 'sent_at_str']
    rename_columns = {
        'ticket_number': 'Ticket',
        'subject': 'Assunto',
        'agent': 'Agente',
        'sent_at_str': 'Data/Hora'
    }

    # Criar componentes de paginação
    total_emails = len(table_df)
    emails_per_page = 100
    total_pages = (total_emails + emails_per_page - 1) // emails_per_page  # Arredonda para cima

    # Criar dropdown para seleção de página
    page_options = [{'label': f'Página {i+1} de {total_pages}', 'value': i} for i in range(total_pages)]

    if total_pages > 0:
        # Mostrar a primeira página por padrão
        current_page_data = table_df.iloc[0:emails_per_page]

        table = dbc.Table.from_dataframe(
            current_page_data[display_columns].rename(columns=rename_columns),
            striped=True,
            bordered=True,
            hover=True,
            responsive=True,
            className="mt-3"
        )

        pagination = html.Div([
            html.Div([
                html.Span(f"Total de {total_emails} emails no período selecionado. ", className="mr-2"),
                dcc.Dropdown(
                    id='page-selector',
                    options=page_options,
                    value=0,
                    clearable=False,
                    style={'width': '200px', 'display': 'inline-block'}
                )
            ], className="d-flex align-items-center mb-2"),
            html.Div(id='pagination-content', children=[table])
        ])

        table_section = [
            html.H5("Todos os E-mails do Período"),
            pagination
        ]
    else:
        table_section = [
            html.H5("Todos os E-mails do Período"),
            html.P("Nenhum email encontrado no período selecionado.")
        ]

    return fig1, fig2, fig3, fig4, fig5, fig6, stats_section, daily_detail_section, table_section

# Callback para atualizar a tabela quando o usuário mudar de página
@app.callback(
    Output('pagination-content', 'children'),
    [Input('page-selector', 'value'),
     Input('agent-filter', 'value'),
     Input('date-range', 'start_date'),
     Input('date-range', 'end_date')]
)
def update_table_page(page_index, selected_agent, start_date, end_date):
    # Carregar e filtrar os dados
    df = load_data()

    if df.empty:
        return [html.P("Nenhum dado disponível")]

    # Filtrar por agente
    if selected_agent != 'all':
        filtered_df = df[df['agent'] == selected_agent]
    else:
        filtered_df = df

    # Filtrar por data
    if start_date and end_date:
        start_date = pd.to_datetime(start_date).date()
        end_date = pd.to_datetime(end_date).date()
        filtered_df = filtered_df[(filtered_df['sent_at'].dt.date >= start_date) &
                                 (filtered_df['sent_at'].dt.date <= end_date)]

    if filtered_df.empty:
        return [html.P("Nenhum email encontrado para os filtros selecionados.")]

    # Preparar os dados para a tabela
    table_df = filtered_df.sort_values('sent_at', ascending=False).copy()
    table_df['sent_at_str'] = table_df['sent_at'].dt.strftime('%d/%m/%Y %H:%M:%S')

    # Selecionar as colunas para a tabela
    display_columns = ['ticket_number', 'subject', 'agent', 'sent_at_str']
    rename_columns = {
        'ticket_number': 'Ticket',
        'subject': 'Assunto',
        'agent': 'Agente',
        'sent_at_str': 'Data/Hora'
    }

    # Calcular o índice inicial e final para a página atual
    emails_per_page = 100
    start_idx = page_index * emails_per_page
    end_idx = start_idx + emails_per_page

    # Obter os dados da página atual
    current_page_data = table_df.iloc[start_idx:end_idx]

    # Criar a tabela
    table = dbc.Table.from_dataframe(
        current_page_data[display_columns].rename(columns=rename_columns),
        striped=True,
        bordered=True,
        hover=True,
        responsive=True,
        className="mt-3"
    )

    return [table]

# Executar a aplicação
if __name__ == '__main__':
    app.run_server(debug=True, host='0.0.0.0', port=8050)
