# Instalação do Dashboard no Servidor

Este documento contém instruções para instalar o Dashboard de Produtividade do Zoho Desk no servidor.

## Requisitos

- Servidor Linux com Nginx
- Python 3.6+
- Certificado SSL configurado para o domínio

## Passos para Instalação

### 1. Preparar os arquivos

Certifique-se de que os seguintes arquivos estão presentes no diretório:

- `dashboard.py` - Aplica<PERSON> Dash
- `start_server.py` - Script para iniciar o servidor
- `requirements.txt` - Dependências Python
- `emails_enviados_abril_2025_v2.csv` - Arquivo de dados
- `dash-report.service` - Arquivo de serviço systemd
- `nginx-dash-config.conf` - Configuração do Nginx
- `install_on_server.sh` - Script de instalação

### 2. Executar o script de instalação

```bash
# Tornar o script executável
chmod +x install_on_server.sh

# Executar o script como root
sudo ./install_on_server.sh
```

### 3. Verificar a instalação

Após a instalação, verifique se:

1. O serviço systemd está rodando:
   ```bash
   sudo systemctl status dash-report
   ```

2. O Nginx está configurado corretamente:
   ```bash
   sudo nginx -t
   ```

3. Acesse o dashboard no navegador:
   ```
   https://suporte.quarktools.com.br/report/
   ```

## Manutenção

### Atualizar os dados

Para atualizar os dados do dashboard, substitua o arquivo CSV no servidor:

```bash
sudo cp novo_arquivo.csv /var/www/suporte.quarktools.com.br/report/emails_enviados_abril_2025_v2.csv
sudo chown www-data:www-data /var/www/suporte.quarktools.com.br/report/emails_enviados_abril_2025_v2.csv
```

### Reiniciar o serviço

Se precisar reiniciar o serviço:

```bash
sudo systemctl restart dash-report
```

### Logs do serviço

Para verificar os logs do serviço:

```bash
sudo journalctl -u dash-report
```
