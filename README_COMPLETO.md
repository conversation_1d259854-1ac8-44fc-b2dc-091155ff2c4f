# 📊 Sistema de Relatórios Zoho Desk

Sistema completo para baixar, processar e visualizar dados de emails enviados do Zoho Desk com dashboard interativo.

## 🚀 Funcionalidades

- ✅ Download automático de todos os emails enviados do Zoho Desk
- ✅ Renovação automática de tokens de acesso
- ✅ Dashboard interativo com múltiplas visualizações
- ✅ Cálculo inteligente de tempo de trabalho por agente
- ✅ Detalhamento diário por agente
- ✅ Filtros por período e agente
- ✅ Mapeamento de nomes fictícios para nomes reais

## 📋 Pré-requisitos

### Python e Dependências
```bash
# Instalar Python 3.8+
# Instalar dependências
pip install requests pandas dash plotly dash-bootstrap-components
```

### Configuração da API Zoho
1. **Client ID**: `1000.668VEFGIXESR0SFQF75XJA5F2ODO`
2. **Client Secret**: `a39c8bd608c1dd61757702d6555049c66e4f39e930`
3. **Token de acesso**: Configurado automaticamente

## 🔧 Configuração Inicial

### 1. Verificar/Renovar Token
```bash
python renovar_token.py
```

### 2. Configurar Agentes (Opcional)
```bash
python gerenciar_agentes.py
```

## 📥 Como Baixar Relatórios

### Opção 1: Download Completo Automatizado (Recomendado)
```bash
python baixar_relatorio_completo.py
```
**O que faz:**
- ✅ Verifica e renova o token automaticamente
- ✅ Baixa todos os emails do histórico disponível
- ✅ Salva em `emails_enviados_historico_completo.csv`
- ✅ Inicia o dashboard automaticamente

### Opção 2: Download Manual Simples
```bash
python app_simples.py
```
**O que faz:**
- ✅ Download direto sem verificações extras
- ✅ Menos logs, mais rápido
- ✅ Ideal para atualizações rápidas

### Opção 3: Download com Logs Detalhados
```bash
python app.py
```
**O que faz:**
- ✅ Logs detalhados do processo
- ✅ Debug de problemas
- ✅ Informações sobre cada ticket processado

## 📊 Visualizar Dashboard

### Iniciar Dashboard
```bash
python dashboard.py
```

### Acessar no Navegador
```
http://localhost:8050/report/
```

### Atualizar Dashboard com Novos Dados
```bash
python atualizar_completo.py
```
**O que faz:**
- ✅ Para o dashboard atual
- ✅ Baixa dados atualizados
- ✅ Reinicia o dashboard automaticamente

## 📈 Funcionalidades do Dashboard

### 🎯 Visualizações Disponíveis
1. **Emails por Agente** - Gráfico de barras
2. **Emails por Hora** - Distribuição temporal
3. **Heatmap Agente x Hora** - Mapa de calor
4. **Emails por Dia da Semana** - Padrões semanais
5. **Tempo de Resposta** - Primeira e última resposta
6. **Tempo de Trabalho** - Cálculo inteligente por blocos
7. **Detalhamento Diário** - Horários e atividades por dia

### 🔍 Filtros Disponíveis
- **Por Agente**: Todos os agentes ou agente específico
- **Por Período**: Seleção de data início e fim
- **Atualização**: Botão para recarregar dados

### ⏱️ Metodologia de Cálculo de Tempo
- **Blocos contínuos**: Gap máximo de 1h entre emails
- **Tempo mínimo**: 1 hora por bloco de trabalho
- **Duração aplicada**: Se bloco < 1h, conta como 1h
- **Total**: Soma de todos os blocos com mínimo aplicado

## 🗂️ Estrutura de Arquivos

```
📁 Projeto/
├── 📄 app.py                          # Download com logs detalhados
├── 📄 app_simples.py                  # Download simples e rápido
├── 📄 baixar_relatorio_completo.py    # Automação completa
├── 📄 atualizar_completo.py           # Atualizar e reiniciar dashboard
├── 📄 dashboard.py                    # Dashboard interativo
├── 📄 renovar_token.py                # Gerenciar tokens de acesso
├── 📄 gerenciar_agentes.py            # Gerenciar mapeamento de agentes
├── 📄 testar_calculo_tempo.py         # Testar lógica de cálculo
├── 📄 emails_enviados_historico_completo.csv  # Dados baixados
└── 📄 README_COMPLETO.md              # Este arquivo
```

## 👥 Gerenciar Agentes

### Adicionar Novo Agente
```bash
python gerenciar_agentes.py
# Escolha opção 2: Adicionar agente
# Digite o nome fictício (como aparece no Zoho)
# Digite o nome real
```

### Listar Agentes Atuais
```bash
python gerenciar_agentes.py
# Escolha opção 1: Listar agentes
```

### Agentes Configurados
- **Jade Fierro** → Dayane Garcia
- **Isabella Carter** → Renato Ponce
- **Emma Collins** → André Pereira
- **Julia Foster** → Jamille Barbosa
- **Sofia Bianchi** → Edyliana

## 🔄 Fluxo de Trabalho Recomendado

### 1. Primeira Execução
```bash
# 1. Verificar token
python renovar_token.py

# 2. Download completo
python baixar_relatorio_completo.py

# 3. Acessar dashboard
# http://localhost:8050/report/
```

### 2. Atualizações Diárias
```bash
# Opção rápida - tudo automatizado
python atualizar_completo.py
```

### 3. Atualizações Manuais
```bash
# 1. Baixar novos dados
python app_simples.py

# 2. Reiniciar dashboard
# Ctrl+C no terminal do dashboard
python dashboard.py
```

## 🛠️ Solução de Problemas

### Token Expirado
```bash
python renovar_token.py
```

### Dashboard não Atualiza
```bash
# Parar dashboard: Ctrl+C
# Reiniciar:
python dashboard.py
```

### Arquivo CSV Vazio
```bash
# Verificar token
python renovar_token.py

# Tentar download novamente
python app_simples.py
```

### Erro de Encoding (Windows)
```bash
# Usar versão simples
python app_simples.py
```

## 📞 Suporte

### Logs e Debug
- Use `app.py` para logs detalhados
- Use `testar_calculo_tempo.py` para testar cálculos
- Verifique o arquivo CSV gerado

### Configurações Importantes
- **Período padrão**: Todo o histórico disponível
- **Fuso horário**: Ajuste automático de -3 horas
- **Formato de data**: DD/MM/YYYY
- **Porta do dashboard**: 8050

## 🎯 Exemplos de Uso

### Download Rápido
```bash
python app_simples.py && python dashboard.py
```

### Atualização Completa
```bash
python atualizar_completo.py
```

### Verificar Configuração
```bash
python renovar_token.py && python gerenciar_agentes.py
```

## 🌐 Configuração para Servidor (Produção)

### No Servidor Linux
```bash
# 1. Instalar dependências
pip3 install requests pandas dash plotly dash-bootstrap-components

# 2. Executar em background
nohup python3 dashboard.py > dashboard.log 2>&1 &

# 3. Verificar se está rodando
ps aux | grep dashboard

# 4. Parar o dashboard
pkill -f dashboard.py
```

### Configurar como Serviço (Systemd)
```bash
# Criar arquivo de serviço
sudo nano /etc/systemd/system/dash-report.service

# Conteúdo do arquivo:
[Unit]
Description=Zoho Desk Dashboard
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/caminho/para/projeto
ExecStart=/usr/bin/python3 dashboard.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target

# Ativar serviço
sudo systemctl daemon-reload
sudo systemctl enable dash-report
sudo systemctl start dash-report
sudo systemctl status dash-report
```

## 📊 Detalhes Técnicos

### Cálculo de Tempo de Trabalho
```
Exemplo prático:
Emails: 09:00, 09:30, 10:45, 12:30

Análise:
- 09:00 → 09:30 (gap: 30min ≤ 1h) ✅ mesmo bloco
- 09:30 → 10:45 (gap: 1h15min > 1h) ❌ novo bloco
- 10:45 → 12:30 (gap: 1h45min > 1h) ❌ novo bloco

Resultado:
- Bloco 1: 09:00-09:30 (30min) → conta 1h (mínimo)
- Bloco 2: 10:45 (1 email) → conta 1h (mínimo)
- Bloco 3: 12:30 (1 email) → conta 1h (mínimo)
- Total: 3h
```

### Estrutura dos Dados CSV
```csv
ticket_number,subject,agent,sent_at
12345,"Problema resolvido",Dayane Garcia,2025-01-15 09:30:00
12346,"Dúvida esclarecida",Renato Ponce,2025-01-15 10:15:00
```

### Mapeamento de Agentes
O sistema mapeia automaticamente nomes fictícios para nomes reais:
- Facilita a identificação dos agentes
- Mantém consistência nos relatórios
- Permite atualizações fáceis via `gerenciar_agentes.py`

## 🔐 Segurança e Tokens

### Renovação Automática
- Tokens expiram periodicamente
- Sistema detecta automaticamente tokens expirados
- Renovação transparente via `renovar_token.py`

### Credenciais da API
- **Client ID**: Identificador único da aplicação
- **Client Secret**: Chave secreta para autenticação
- **Access Token**: Token temporário para acesso à API

### Backup e Recuperação
```bash
# Fazer backup dos dados
cp emails_enviados_historico_completo.csv backup_$(date +%Y%m%d).csv

# Restaurar backup
cp backup_20250115.csv emails_enviados_historico_completo.csv
```

## 📱 Acesso Remoto

### Configurar Acesso Externo
```python
# No dashboard.py, alterar:
app.run_server(debug=False, host='0.0.0.0', port=8050)

# Acessar de qualquer lugar:
http://SEU_IP:8050/report/
```

### Proxy Reverso (Nginx)
```nginx
server {
    listen 80;
    server_name seu-dominio.com;

    location /report/ {
        proxy_pass http://localhost:8050/report/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🚨 Monitoramento

### Verificar Status
```bash
# Verificar se o dashboard está rodando
curl http://localhost:8050/report/

# Verificar logs
tail -f dashboard.log

# Verificar uso de recursos
top | grep python
```

### Alertas Automáticos
```bash
# Script para verificar se está rodando
#!/bin/bash
if ! pgrep -f "dashboard.py" > /dev/null; then
    echo "Dashboard parado! Reiniciando..."
    python3 dashboard.py &
fi
```

---

**💡 Dica**: Use sempre `atualizar_completo.py` para ter certeza de que tudo está sincronizado!

**🔧 Para Produção**: Configure como serviço systemd para inicialização automática!

**📊 Dashboard URL**: http://localhost:8050/report/
