#!/usr/bin/env python3
"""
Script para diagnosticar problemas no download dos dados do Zoho Desk
"""

import os
import requests
import json
from datetime import datetime

# Configurações
ACCESS_TOKEN = "**********************************************************************"
BASE_URL = "https://desk.zoho.com/api/v1"

HEADERS = {
    "Authorization": f"Zoho-oauthtoken {ACCESS_TOKEN}",
    "Content-Type": "application/json"
}

def verificar_arquivo_csv():
    """Verifica o estado do arquivo CSV"""
    arquivo = "emails_enviados_historico_completo.csv"
    
    print("=" * 60)
    print("VERIFICAÇÃO DO ARQUIVO CSV")
    print("=" * 60)
    
    if os.path.exists(arquivo):
        tamanho = os.path.getsize(arquivo)
        modificado = datetime.fromtimestamp(os.path.getmtime(arquivo))
        
        print(f"✅ Arquivo existe: {arquivo}")
        print(f"📏 Tamanho: {tamanho} bytes")
        print(f"📅 Última modificação: {modificado.strftime('%d/%m/%Y %H:%M:%S')}")
        
        if tamanho < 1000:
            print("⚠️ PROBLEMA: Arquivo muito pequeno (< 1KB)")
            return False
        elif tamanho < 10000:
            print("⚠️ AVISO: Arquivo pequeno (< 10KB)")
        else:
            print("✅ Tamanho do arquivo parece normal")
        
        # Verificar conteúdo
        try:
            with open(arquivo, 'r', encoding='utf-8') as f:
                linhas = f.readlines()
                print(f"📄 Número de linhas: {len(linhas)}")
                
                if len(linhas) > 1:
                    print("✅ Arquivo tem dados além do cabeçalho")
                    print(f"📝 Primeira linha de dados: {linhas[1][:100]}...")
                else:
                    print("❌ PROBLEMA: Arquivo só tem cabeçalho, sem dados")
                    return False
        except Exception as e:
            print(f"❌ ERRO ao ler arquivo: {e}")
            return False
            
        return True
    else:
        print(f"❌ Arquivo não existe: {arquivo}")
        return False

def testar_token():
    """Testa se o token está funcionando"""
    print("\n" + "=" * 60)
    print("TESTE DO TOKEN DE ACESSO")
    print("=" * 60)
    
    try:
        # Teste simples: buscar informações da organização
        response = requests.get(f"{BASE_URL}/organizations", headers=HEADERS, timeout=30)
        
        print(f"🌐 Status da resposta: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Token está funcionando!")
            data = response.json()
            if 'data' in data:
                print(f"📊 Dados retornados: {len(data['data'])} organizações")
            return True
        elif response.status_code == 401:
            print("❌ PROBLEMA: Token inválido ou expirado")
            print("💡 Solução: Execute 'python renovar_token.py'")
            return False
        elif response.status_code == 403:
            print("❌ PROBLEMA: Token sem permissões suficientes")
            return False
        else:
            print(f"⚠️ PROBLEMA: Status inesperado {response.status_code}")
            print(f"📄 Resposta: {response.text[:200]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ PROBLEMA: Timeout na conexão")
        print("💡 Verifique sua conexão com a internet")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ PROBLEMA: Erro de conexão")
        print("💡 Verifique sua conexão com a internet")
        return False
    except Exception as e:
        print(f"❌ ERRO inesperado: {e}")
        return False

def testar_busca_tickets():
    """Testa a busca de tickets"""
    print("\n" + "=" * 60)
    print("TESTE DE BUSCA DE TICKETS")
    print("=" * 60)
    
    try:
        # Buscar apenas os primeiros 5 tickets para teste
        params = {"from": 1, "limit": 5}
        response = requests.get(f"{BASE_URL}/tickets", headers=HEADERS, params=params, timeout=30)
        
        print(f"🌐 Status da resposta: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            tickets = data.get("data", [])
            print(f"✅ Busca funcionando! Encontrados {len(tickets)} tickets")
            
            if tickets:
                primeiro_ticket = tickets[0]
                print(f"📋 Primeiro ticket ID: {primeiro_ticket.get('id')}")
                print(f"📋 Número do ticket: {primeiro_ticket.get('ticketNumber')}")
                print(f"📋 Assunto: {primeiro_ticket.get('subject', 'Sem assunto')[:50]}...")
                return True
            else:
                print("⚠️ PROBLEMA: Nenhum ticket retornado")
                return False
        else:
            print(f"❌ PROBLEMA: Status {response.status_code}")
            print(f"📄 Resposta: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ ERRO: {e}")
        return False

def testar_busca_threads():
    """Testa a busca de threads de um ticket"""
    print("\n" + "=" * 60)
    print("TESTE DE BUSCA DE THREADS")
    print("=" * 60)
    
    try:
        # Primeiro, buscar um ticket
        params = {"from": 1, "limit": 1}
        response = requests.get(f"{BASE_URL}/tickets", headers=HEADERS, params=params, timeout=30)
        
        if response.status_code != 200:
            print("❌ Não foi possível buscar tickets para teste")
            return False
            
        tickets = response.json().get("data", [])
        if not tickets:
            print("❌ Nenhum ticket disponível para teste")
            return False
            
        ticket_id = tickets[0]["id"]
        print(f"🎫 Testando com ticket ID: {ticket_id}")
        
        # Buscar threads do ticket
        response = requests.get(f"{BASE_URL}/tickets/{ticket_id}/threads", headers=HEADERS, timeout=30)
        
        print(f"🌐 Status da resposta: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            threads = data.get("data", [])
            print(f"✅ Busca de threads funcionando! Encontrados {len(threads)} threads")
            
            # Analisar threads
            emails_enviados = 0
            for thread in threads:
                if thread.get("channel") == "EMAIL" and thread.get("direction") == "out":
                    emails_enviados += 1
            
            print(f"📧 Emails enviados neste ticket: {emails_enviados}")
            return True
        else:
            print(f"❌ PROBLEMA: Status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ ERRO: {e}")
        return False

def verificar_conectividade():
    """Verifica conectividade básica"""
    print("\n" + "=" * 60)
    print("TESTE DE CONECTIVIDADE")
    print("=" * 60)
    
    try:
        # Teste básico de conectividade
        response = requests.get("https://desk.zoho.com", timeout=10)
        print(f"🌐 Conexão com Zoho Desk: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Conectividade OK")
            return True
        else:
            print("⚠️ Problema de conectividade")
            return False
            
    except Exception as e:
        print(f"❌ ERRO de conectividade: {e}")
        return False

def main():
    """Executa todos os diagnósticos"""
    print("🔍 DIAGNÓSTICO COMPLETO DO SISTEMA ZOHO DESK")
    print("=" * 80)
    print(f"⏰ Executado em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 80)
    
    resultados = {
        "arquivo_csv": verificar_arquivo_csv(),
        "conectividade": verificar_conectividade(),
        "token": testar_token(),
        "busca_tickets": False,
        "busca_threads": False
    }
    
    # Só testa busca se o token estiver OK
    if resultados["token"]:
        resultados["busca_tickets"] = testar_busca_tickets()
        
        # Só testa threads se a busca de tickets estiver OK
        if resultados["busca_tickets"]:
            resultados["busca_threads"] = testar_busca_threads()
    
    # Resumo final
    print("\n" + "=" * 80)
    print("RESUMO DO DIAGNÓSTICO")
    print("=" * 80)
    
    for teste, resultado in resultados.items():
        status = "✅ OK" if resultado else "❌ PROBLEMA"
        print(f"{teste.replace('_', ' ').title()}: {status}")
    
    # Recomendações
    print("\n" + "=" * 80)
    print("RECOMENDAÇÕES")
    print("=" * 80)
    
    if not resultados["token"]:
        print("🔧 1. Execute: python renovar_token.py")
    
    if not resultados["arquivo_csv"]:
        print("🔧 2. Execute: python app_simples.py")
    
    if not resultados["conectividade"]:
        print("🔧 3. Verifique sua conexão com a internet")
    
    if all(resultados.values()):
        print("🎉 Tudo funcionando perfeitamente!")
        print("💡 Se ainda há problemas, execute: python app_simples.py")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
