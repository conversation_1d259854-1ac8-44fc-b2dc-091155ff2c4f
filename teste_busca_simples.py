#!/usr/bin/env python3
"""
Teste simples para verificar se conseguimos buscar dados do Zoho Desk
"""

import requests
import csv
from datetime import datetime

# Token atualizado
ACCESS_TOKEN = "**********************************************************************"

HEADERS = {
    "Authorization": f"Zoho-oauthtoken {ACCESS_TOKEN}",
    "Content-Type": "application/json"
}

BASE_URL = "https://desk.zoho.com/api/v1"

def teste_buscar_tickets():
    """Teste básico para buscar tickets"""
    print("=" * 60)
    print("TESTE DE BUSCA DE TICKETS")
    print("=" * 60)
    
    try:
        # Buscar apenas 5 tickets para teste
        params = {"from": 1, "limit": 5}
        print(f"Fazendo requisição para: {BASE_URL}/tickets")
        print(f"Parâmetros: {params}")
        print(f"Headers: Authorization com token...")
        
        response = requests.get(f"{BASE_URL}/tickets", headers=HEADERS, params=params, timeout=30)
        
        print(f"Status da resposta: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            tickets = data.get("data", [])
            print(f"✅ Sucesso! Encontrados {len(tickets)} tickets")
            
            if tickets:
                for i, ticket in enumerate(tickets, 1):
                    print(f"  {i}. Ticket #{ticket.get('ticketNumber')} - {ticket.get('subject', 'Sem assunto')[:50]}...")
                return tickets
            else:
                print("⚠️ Nenhum ticket retornado")
                return []
        else:
            print(f"❌ Erro: {response.status_code}")
            print(f"Resposta: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Erro na requisição: {e}")
        return None

def teste_buscar_threads(ticket_id):
    """Teste para buscar threads de um ticket específico"""
    print(f"\n--- Testando threads do ticket {ticket_id} ---")
    
    try:
        response = requests.get(f"{BASE_URL}/tickets/{ticket_id}/threads", headers=HEADERS, timeout=30)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            threads = data.get("data", [])
            print(f"✅ Encontrados {len(threads)} threads")
            
            emails_enviados = 0
            for thread in threads:
                is_email = thread.get("channel") == "EMAIL"
                is_outgoing = thread.get("direction") == "out"
                
                if is_email and is_outgoing:
                    emails_enviados += 1
                    print(f"  📧 Email enviado: {thread.get('createdTime', 'Sem data')}")
            
            print(f"Total de emails enviados neste ticket: {emails_enviados}")
            return threads
        else:
            print(f"❌ Erro: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return []

def teste_salvar_csv():
    """Teste para salvar dados em CSV"""
    print("\n" + "=" * 60)
    print("TESTE DE SALVAMENTO EM CSV")
    print("=" * 60)
    
    # Buscar alguns tickets
    tickets = teste_buscar_tickets()
    
    if not tickets:
        print("❌ Não foi possível buscar tickets para teste")
        return False
    
    # Processar alguns tickets
    dados_para_csv = []
    
    for ticket in tickets[:3]:  # Apenas os primeiros 3 para teste
        ticket_id = ticket["id"]
        ticket_number = ticket["ticketNumber"]
        subject = ticket.get("subject", "Sem assunto")
        
        print(f"\nProcessando ticket #{ticket_number}...")
        
        threads = teste_buscar_threads(ticket_id)
        
        for thread in threads:
            is_email = thread.get("channel") == "EMAIL"
            is_outgoing = thread.get("direction") == "out"
            
            if is_email and is_outgoing:
                try:
                    # Tentar parsear a data
                    created_time = thread.get("createdTime")
                    if created_time:
                        try:
                            sent_time = datetime.strptime(created_time, "%Y-%m-%dT%H:%M:%S.%fZ")
                        except:
                            try:
                                sent_time = datetime.strptime(created_time, "%Y-%m-%dT%H:%M:%SZ")
                            except:
                                print(f"  ⚠️ Erro ao parsear data: {created_time}")
                                continue
                        
                        # Obter nome do agente
                        agent_name = "Desconhecido"
                        if "author" in thread and isinstance(thread["author"], dict):
                            agent_name = thread["author"].get("name", "Desconhecido")
                        
                        dados_para_csv.append({
                            "ticket_number": ticket_number,
                            "subject": subject,
                            "agent": agent_name,
                            "sent_at": sent_time.strftime("%Y-%m-%d %H:%M:%S")
                        })
                        
                        print(f"  ✅ Email adicionado: {agent_name} em {sent_time.strftime('%d/%m/%Y %H:%M')}")
                        
                except Exception as e:
                    print(f"  ❌ Erro ao processar thread: {e}")
    
    # Salvar em CSV
    if dados_para_csv:
        arquivo_teste = "teste_emails.csv"
        try:
            with open(arquivo_teste, "w", newline="", encoding="utf-8") as f:
                writer = csv.DictWriter(f, fieldnames=["ticket_number", "subject", "agent", "sent_at"])
                writer.writeheader()
                writer.writerows(dados_para_csv)
            
            print(f"\n✅ Arquivo {arquivo_teste} criado com sucesso!")
            print(f"📊 {len(dados_para_csv)} emails salvos")
            
            # Verificar o arquivo
            import os
            tamanho = os.path.getsize(arquivo_teste)
            print(f"📏 Tamanho do arquivo: {tamanho} bytes")
            
            return True
            
        except Exception as e:
            print(f"❌ Erro ao salvar CSV: {e}")
            return False
    else:
        print("❌ Nenhum dado para salvar")
        return False

def main():
    """Executa todos os testes"""
    print("🔍 TESTE SIMPLES DE BUSCA E SALVAMENTO")
    print("=" * 80)
    print(f"⏰ Executado em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 80)
    
    # Teste 1: Buscar tickets
    tickets = teste_buscar_tickets()
    
    if tickets is None:
        print("\n❌ FALHA: Não foi possível buscar tickets")
        print("Verifique:")
        print("1. Se o token está correto")
        print("2. Se você tem acesso à API")
        print("3. Se a URL base está correta")
        return
    
    if not tickets:
        print("\n⚠️ AVISO: Nenhum ticket encontrado")
        return
    
    # Teste 2: Buscar threads do primeiro ticket
    primeiro_ticket = tickets[0]
    threads = teste_buscar_threads(primeiro_ticket["id"])
    
    # Teste 3: Salvar em CSV
    sucesso_csv = teste_salvar_csv()
    
    # Resumo
    print("\n" + "=" * 80)
    print("RESUMO DOS TESTES")
    print("=" * 80)
    print(f"✅ Busca de tickets: {'OK' if tickets else 'FALHA'}")
    print(f"✅ Busca de threads: {'OK' if threads else 'FALHA'}")
    print(f"✅ Salvamento CSV: {'OK' if sucesso_csv else 'FALHA'}")
    
    if sucesso_csv:
        print("\n🎉 Todos os testes passaram!")
        print("💡 O problema pode estar na lógica do script principal")
    else:
        print("\n❌ Há problemas na busca ou salvamento")
        print("💡 Verifique o token e as permissões")

if __name__ == "__main__":
    main()
