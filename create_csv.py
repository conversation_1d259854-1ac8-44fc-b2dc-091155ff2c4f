import csv

# Dados de exemplo baseados no que vimos nos logs
data = [
    {"ticket_number": "1806", "subject": "Tracking", "agent": "<PERSON>", "sent_at": "2025-05-01 18:56:22"},
    {"ticket_number": "1663", "subject": "Missing items", "agent": "<PERSON>", "sent_at": "2025-05-01 18:59:50"},
    {"ticket_number": "1663", "subject": "Missing items", "agent": "<PERSON>", "sent_at": "2025-04-29 20:10:22"},
    {"ticket_number": "1663", "subject": "Missing items", "agent": "<PERSON>", "sent_at": "2025-04-28 23:09:56"},
    {"ticket_number": "1810", "subject": "#154382 - Return Item", "agent": "<PERSON>", "sent_at": "2025-05-01 18:47:10"},
    {"ticket_number": "1794", "subject": "Re: <PERSON>, Your order 188908 is...", "agent": "<PERSON>", "sent_at": "2025-05-01 18:21:22"},
    {"ticket_number": "1589", "subject": "Re: <PERSON><PERSON>, your exclusive cashback is about to expire!", "agent": "<PERSON> <PERSON>erro", "sent_at": "2025-05-01 18:50:57"},
    {"ticket_number": "1589", "subject": "Re: <PERSON>and<PERSON>, your exclusive cashback is about to expire!", "agent": "<PERSON> Fierro", "sent_at": "2025-04-29 19:13:14"},
    {"ticket_number": "1589", "subject": "Re: <PERSON>and<PERSON>, your exclusive cashback is about to expire!", "agent": "Jade Fierro", "sent_at": "2025-04-29 16:19:04"},
    {"ticket_number": "1589", "subject": "Re: Alexandros, your exclusive cashback is about to expire!", "agent": "Jade Fierro", "sent_at": "2025-04-27 15:10:23"},
    {"ticket_number": "1589", "subject": "Re: Alexandros, your exclusive cashback is about to expire!", "agent": "Jade Fierro", "sent_at": "2025-04-26 14:08:12"},
    {"ticket_number": "1800", "subject": "Re: Roshan, Your order has been delivered!", "agent": "Julia Foster", "sent_at": "2025-05-01 19:00:33"},
    {"ticket_number": "1800", "subject": "Re: Roshan, Your order has been delivered!", "agent": "Julia Foster", "sent_at": "2025-05-01 18:27:56"},
    {"ticket_number": "1812", "subject": "Anfrage", "agent": "Jade Fierro", "sent_at": "2025-05-01 19:00:29"},
    {"ticket_number": "1616", "subject": "Nachfrage", "agent": "Julia Foster", "sent_at": "2025-04-30 21:09:08"},
    {"ticket_number": "1616", "subject": "Nachfrage", "agent": "Julia Foster", "sent_at": "2025-04-29 20:48:25"}
]

# Escrever os dados no arquivo CSV
with open('emails_enviados_abril_2025_new.csv', 'w', newline='', encoding='utf-8') as f:
    writer = csv.DictWriter(f, fieldnames=["ticket_number", "subject", "agent", "sent_at"])
    writer.writeheader()
    writer.writerows(data)

print("Arquivo CSV criado com sucesso!")
