#!/usr/bin/env python3
"""
Script para baixar dados do Zoho e reiniciar o dashboard automaticamente
"""

import subprocess
import sys
import os
import time
import signal
from datetime import datetime

def executar_comando(comando, descricao, wait=True):
    """Executa um comando e mostra o resultado"""
    print(f"\n{'='*60}")
    print(f"EXECUTANDO: {descricao}")
    print(f"{'='*60}")
    
    try:
        if wait:
            resultado = subprocess.run(comando, shell=True, capture_output=True, text=True)
            
            if resultado.stdout:
                print("SAÍDA:")
                print(resultado.stdout)
            
            if resultado.stderr:
                print("ERROS:")
                print(resultado.stderr)
            
            return resultado.returncode == 0
        else:
            # Para processos que não devem esperar (como o dashboard)
            processo = subprocess.Popen(comando, shell=True)
            return processo
            
    except Exception as e:
        print(f"Erro ao executar comando: {e}")
        return False

def parar_dashboard():
    """Para qualquer processo do dashboard que esteja rodando"""
    print("\n[STOP] Parando dashboard existente...")
    
    try:
        # No Windows, usar taskkill para parar processos Python que estão rodando dashboard.py
        subprocess.run("taskkill /f /im python.exe", shell=True, capture_output=True)
        time.sleep(2)
        print("[OK] Processos anteriores finalizados")
    except:
        print("[INFO] Nenhum processo anterior encontrado")

def verificar_arquivo_dados():
    """Verifica se o arquivo de dados existe e mostra informações"""
    arquivo = "emails_enviados_historico_completo.csv"
    
    if os.path.exists(arquivo):
        tamanho = os.path.getsize(arquivo)
        modificado = datetime.fromtimestamp(os.path.getmtime(arquivo))
        
        print(f"\n[ARQUIVO] {arquivo}")
        print(f"[TAMANHO] {tamanho} bytes")
        print(f"[MODIFICADO] {modificado.strftime('%d/%m/%Y %H:%M:%S')}")
        
        if tamanho > 1000:  # Mais de 1KB
            print("[STATUS] Arquivo parece ter dados válidos")
            return True
        else:
            print("[AVISO] Arquivo muito pequeno, pode estar vazio")
            return False
    else:
        print(f"[ERRO] Arquivo {arquivo} não encontrado")
        return False

def main():
    """Função principal"""
    print("="*80)
    print("ATUALIZADOR COMPLETO - ZOHO DESK DASHBOARD")
    print("="*80)
    print(f"Horário: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("="*80)
    
    # Passo 1: Parar dashboard existente
    parar_dashboard()
    
    # Passo 2: Verificar/renovar token
    print("\n[PASSO 1] Verificando token...")
    if not executar_comando("python renovar_token.py", "Verificar Token"):
        print("[ERRO] Problema com o token")
        return
    
    # Passo 3: Baixar dados
    print("\n[PASSO 2] Baixando dados...")
    if not executar_comando("python app_simples.py", "Download de Dados"):
        print("[ERRO] Falha no download")
        return
    
    # Passo 4: Verificar arquivo gerado
    print("\n[PASSO 3] Verificando arquivo...")
    if not verificar_arquivo_dados():
        print("[AVISO] Arquivo pode estar vazio, mas continuando...")
    
    # Passo 5: Iniciar dashboard
    print("\n[PASSO 4] Iniciando dashboard...")
    print("Aguarde alguns segundos para o dashboard inicializar...")
    
    processo_dashboard = executar_comando("python dashboard.py", "Iniciar Dashboard", wait=False)
    
    if processo_dashboard:
        time.sleep(5)  # Aguarda 5 segundos para o dashboard inicializar
        
        print("\n" + "="*80)
        print("PROCESSO CONCLUÍDO!")
        print("="*80)
        print("Dashboard iniciado com sucesso!")
        print("\nAcesse: http://localhost:8050/report/")
        print("\nPara parar o dashboard, pressione Ctrl+C neste terminal")
        print("="*80)
        
        try:
            # Mantém o script rodando para monitorar o dashboard
            while True:
                time.sleep(10)
                # Verifica se o processo ainda está rodando
                if processo_dashboard.poll() is not None:
                    print("\n[AVISO] Dashboard parou de funcionar")
                    break
        except KeyboardInterrupt:
            print("\n[STOP] Parando dashboard...")
            processo_dashboard.terminate()
            print("[OK] Dashboard parado")
    else:
        print("[ERRO] Falha ao iniciar dashboard")

if __name__ == "__main__":
    main()
