import requests
import json
from datetime import datetime

# Token de acesso à API do Zoho Desk
ACCESS_TOKEN = "**********************************************************************"

HEADERS = {
    "Authorization": f"Zoho-oauthtoken {ACCESS_TOKEN}",
    "Content-Type": "application/json"
}

BASE_URL = "https://desk.zoho.com/api/v1"

def get_ticket(ticket_id):
    """Busca um ticket específico pelo ID"""
    url = f"{BASE_URL}/tickets/{ticket_id}"
    try:
        response = requests.get(url, headers=HEADERS)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Erro ao buscar ticket {ticket_id}: {e}")
        return None

def get_threads(ticket_id):
    """Busca todos os threads de um ticket específico"""
    url = f"{BASE_URL}/tickets/{ticket_id}/threads"
    try:
        response = requests.get(url, headers=HEADERS)
        response.raise_for_status()
        return response.json().get("data", [])
    except Exception as e:
        print(f"Erro ao buscar threads do ticket {ticket_id}: {e}")
        return []

def get_recent_tickets(limit=5):
    """Busca os tickets mais recentes"""
    url = f"{BASE_URL}/tickets"
    params = {"from": 1, "limit": limit}
    try:
        response = requests.get(url, headers=HEADERS, params=params)
        response.raise_for_status()
        return response.json().get("data", [])
    except Exception as e:
        print(f"Erro ao buscar tickets recentes: {e}")
        return []

# Busca alguns tickets recentes
print("Buscando tickets recentes...")
tickets = get_recent_tickets(5)
print(f"Encontrados {len(tickets)} tickets recentes")

# Para cada ticket, busca e exibe os threads
for ticket in tickets:
    ticket_id = ticket["id"]
    ticket_number = ticket["ticketNumber"]
    subject = ticket["subject"]

    print(f"\n\n{'='*80}")
    print(f"TICKET #{ticket_number}: {subject}")
    print(f"ID: {ticket_id}")
    print(f"{'='*80}")

    threads = get_threads(ticket_id)
    print(f"Encontrados {len(threads)} threads")

    for i, thread in enumerate(threads):
        print(f"\n--- THREAD {i+1} ---")
        print(f"Chaves disponíveis: {list(thread.keys())}")

        # Exibe os principais campos
        for key in ['id', 'channel', 'direction', 'type', 'createdTime', 'createdBy']:
            if key in thread:
                print(f"{key}: {thread[key]}")

        # Tenta converter a data
        if 'createdTime' in thread:
            try:
                created_time = datetime.strptime(thread["createdTime"], "%Y-%m-%dT%H:%M:%S.%fZ")
                print(f"Data formatada: {created_time.strftime('%d/%m/%Y %H:%M:%S')}")
            except:
                try:
                    created_time = datetime.strptime(thread["createdTime"], "%Y-%m-%dT%H:%M:%SZ")
                    print(f"Data formatada: {created_time.strftime('%d/%m/%Y %H:%M:%S')}")
                except:
                    print(f"Não foi possível formatar a data: {thread['createdTime']}")

        # Exibe o conteúdo do thread
        if 'content' in thread:
            print(f"\nConteúdo: {thread['content'][:200]}...")  # Mostra apenas os primeiros 200 caracteres

        print(f"\nThread completo (formato JSON):")
        print(json.dumps(thread, indent=2)[:500] + "...")  # Mostra apenas os primeiros 500 caracteres

print("\nInspeção concluída!")
