#!/usr/bin/env python3
"""
Script para gerenciar o mapeamento de agentes no dashboard
Permite adicionar, remover ou listar agentes facilmente
"""

import re
import os
from datetime import datetime

DASHBOARD_FILE = "dashboard.py"

def ler_mapeamento_atual():
    """Lê o mapeamento atual de agentes do arquivo dashboard.py"""
    if not os.path.exists(DASHBOARD_FILE):
        print(f"[ERRO] Arquivo {DASHBOARD_FILE} não encontrado!")
        return {}
    
    with open(DASHBOARD_FILE, 'r', encoding='utf-8') as f:
        conteudo = f.read()
    
    # Procurar o bloco AGENT_MAPPING
    padrao = r"AGENT_MAPPING = \{([^}]+)\}"
    match = re.search(padrao, conteudo, re.DOTALL)
    
    if not match:
        print("[ERRO] Não foi possível encontrar AGENT_MAPPING no arquivo!")
        return {}
    
    # Extrair os mapeamentos
    mapeamentos = {}
    linhas = match.group(1).strip().split('\n')
    
    for linha in linhas:
        linha = linha.strip()
        if linha and not linha.startswith('#'):
            # Remover vírgula final se existir
            linha = linha.rstrip(',')
            
            # Extrair chave e valor
            if ':' in linha:
                partes = linha.split(':', 1)
                chave = partes[0].strip().strip("'\"")
                valor = partes[1].strip().strip("'\"")
                mapeamentos[chave] = valor
    
    return mapeamentos

def salvar_mapeamento(mapeamentos):
    """Salva o mapeamento atualizado no arquivo dashboard.py"""
    if not os.path.exists(DASHBOARD_FILE):
        print(f"[ERRO] Arquivo {DASHBOARD_FILE} não encontrado!")
        return False
    
    # Ler o arquivo atual
    with open(DASHBOARD_FILE, 'r', encoding='utf-8') as f:
        conteudo = f.read()
    
    # Criar o novo bloco de mapeamento
    novo_mapeamento = "AGENT_MAPPING = {\n"
    for chave, valor in sorted(mapeamentos.items()):
        novo_mapeamento += f"    '{chave}': '{valor}',\n"
    novo_mapeamento += "}"
    
    # Substituir o mapeamento antigo
    padrao = r"AGENT_MAPPING = \{[^}]+\}"
    novo_conteudo = re.sub(padrao, novo_mapeamento, conteudo, flags=re.DOTALL)
    
    # Fazer backup
    backup_file = f"{DASHBOARD_FILE}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(conteudo)
    print(f"[BACKUP] Backup criado: {backup_file}")
    
    # Salvar o novo arquivo
    with open(DASHBOARD_FILE, 'w', encoding='utf-8') as f:
        f.write(novo_conteudo)
    
    print("[OK] Mapeamento atualizado com sucesso!")
    return True

def listar_agentes():
    """Lista todos os agentes mapeados"""
    mapeamentos = ler_mapeamento_atual()
    
    if not mapeamentos:
        print("[INFO] Nenhum agente encontrado")
        return
    
    print("\n" + "="*60)
    print("AGENTES MAPEADOS")
    print("="*60)
    print(f"{'Nome Fictício':<20} | {'Nome Real':<20}")
    print("-"*60)
    
    for ficticio, real in sorted(mapeamentos.items()):
        print(f"{ficticio:<20} | {real:<20}")
    
    print("="*60)
    print(f"Total: {len(mapeamentos)} agentes")

def adicionar_agente():
    """Adiciona um novo agente ao mapeamento"""
    print("\n[ADICIONAR AGENTE]")
    
    nome_ficticio = input("Nome fictício (como aparece no Zoho): ").strip()
    if not nome_ficticio:
        print("[ERRO] Nome fictício não pode estar vazio!")
        return
    
    nome_real = input("Nome real: ").strip()
    if not nome_real:
        print("[ERRO] Nome real não pode estar vazio!")
        return
    
    mapeamentos = ler_mapeamento_atual()
    
    if nome_ficticio in mapeamentos:
        print(f"[AVISO] Agente '{nome_ficticio}' já existe!")
        print(f"Valor atual: {mapeamentos[nome_ficticio]}")
        
        confirmar = input("Deseja sobrescrever? (s/N): ").strip().lower()
        if confirmar != 's':
            print("[CANCELADO]")
            return
    
    mapeamentos[nome_ficticio] = nome_real
    
    if salvar_mapeamento(mapeamentos):
        print(f"[SUCESSO] Agente adicionado: '{nome_ficticio}' -> '{nome_real}'")

def remover_agente():
    """Remove um agente do mapeamento"""
    print("\n[REMOVER AGENTE]")
    
    mapeamentos = ler_mapeamento_atual()
    if not mapeamentos:
        print("[INFO] Nenhum agente para remover")
        return
    
    print("Agentes disponíveis:")
    for i, (ficticio, real) in enumerate(sorted(mapeamentos.items()), 1):
        print(f"{i}. {ficticio} -> {real}")
    
    try:
        escolha = input("\nDigite o número do agente para remover (ou nome fictício): ").strip()
        
        if escolha.isdigit():
            # Escolha por número
            idx = int(escolha) - 1
            agentes_lista = list(sorted(mapeamentos.keys()))
            if 0 <= idx < len(agentes_lista):
                nome_ficticio = agentes_lista[idx]
            else:
                print("[ERRO] Número inválido!")
                return
        else:
            # Escolha por nome
            nome_ficticio = escolha
            if nome_ficticio not in mapeamentos:
                print(f"[ERRO] Agente '{nome_ficticio}' não encontrado!")
                return
        
        print(f"Removendo: '{nome_ficticio}' -> '{mapeamentos[nome_ficticio]}'")
        confirmar = input("Confirma a remoção? (s/N): ").strip().lower()
        
        if confirmar == 's':
            del mapeamentos[nome_ficticio]
            if salvar_mapeamento(mapeamentos):
                print(f"[SUCESSO] Agente '{nome_ficticio}' removido!")
        else:
            print("[CANCELADO]")
            
    except ValueError:
        print("[ERRO] Entrada inválida!")

def main():
    """Função principal"""
    while True:
        print("\n" + "="*50)
        print("GERENCIADOR DE AGENTES - ZOHO DESK DASHBOARD")
        print("="*50)
        print("1. Listar agentes")
        print("2. Adicionar agente")
        print("3. Remover agente")
        print("4. Sair")
        print("="*50)
        
        escolha = input("Escolha uma opção (1-4): ").strip()
        
        if escolha == '1':
            listar_agentes()
        elif escolha == '2':
            adicionar_agente()
        elif escolha == '3':
            remover_agente()
        elif escolha == '4':
            print("\n[SAINDO] Até logo!")
            break
        else:
            print("[ERRO] Opção inválida! Escolha 1-4.")

if __name__ == "__main__":
    main()
