#!/bin/bash

# Script para instalar a aplicação Dash no servidor

# Verificar se está rodando como root
if [ "$EUID" -ne 0 ]; then
  echo "Por favor, execute este script como root"
  exit 1
fi

# Definir variáveis
APP_DIR="/var/www/suporte.quarktools.com.br/report"
SERVICE_NAME="dash-report"

# Criar diretório da aplicação
echo "Criando diretório da aplicação..."
mkdir -p $APP_DIR

# Copiar arquivos para o diretório da aplicação
echo "Copiando arquivos da aplicação..."
cp -r dashboard.py start_server.py requirements.txt emails_enviados_abril_2025_v2.csv $APP_DIR/

# Verificar e instalar pip3 se necessário
if ! command -v pip3 &> /dev/null; then
    echo "pip3 não encontrado. Instalando..."
    apt-get update
    apt-get install -y python3-pip
fi

# Instalar dependências
echo "Instalando dependências..."
pip3 install -r $APP_DIR/requirements.txt

# Configurar permissões
echo "Configurando permissões..."
chown -R www-data:www-data $APP_DIR
chmod -R 755 $APP_DIR

# Configurar serviço systemd
echo "Configurando serviço systemd..."
cp dash-report.service /etc/systemd/system/$SERVICE_NAME.service
systemctl daemon-reload
systemctl enable $SERVICE_NAME
systemctl start $SERVICE_NAME

# Configurar Nginx
echo "Configurando Nginx..."
cp nginx-dash-config.conf /etc/nginx/sites-available/suporte.quarktools.com.br
ln -sf /etc/nginx/sites-available/suporte.quarktools.com.br /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx

echo "Instalação concluída!"
echo "A aplicação Dash está disponível em: https://suporte.quarktools.com.br/report/"
