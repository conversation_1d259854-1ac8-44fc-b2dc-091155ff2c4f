#!/usr/bin/env python3
"""
Script para testar a nova lógica de cálculo de tempo de trabalho
"""

import pandas as pd
from datetime import datetime, timedelta

def calcular_tempo_trabalho_teste(emails_horarios):
    """
    Versão de teste da função de cálculo de tempo de trabalho
    """
    if not emails_horarios:
        return timedelta(0), []
    
    # Ordenar horários
    emails_horarios.sort()
    
    tempo_total = timedelta(0)
    detalhes_blocos = []
    
    # Criar blocos contínuos de trabalho
    blocos = []
    bloco_atual = [emails_horarios[0]]
    
    for i in range(1, len(emails_horarios)):
        email_anterior = emails_horarios[i-1]
        email_atual = emails_horarios[i]
        
        # Calcular gap entre emails
        gap = email_atual - email_anterior
        
        if gap <= timedelta(hours=1):
            # Continua o bloco atual
            bloco_atual.append(email_atual)
        else:
            # Fecha o bloco anterior e inicia um novo
            blocos.append(bloco_atual)
            bloco_atual = [email_atual]
    
    # Adicionar o último bloco
    if bloco_atual:
        blocos.append(bloco_atual)
    
    # Calcular duração de cada bloco
    for i, bloco in enumerate(blocos):
        if len(bloco) == 1:
            # Bloco com apenas 1 email = tempo mínimo de 1 hora
            duracao_aplicada = timedelta(hours=1)
            duracao_real = timedelta(0)
        else:
            # Calcular duração real do bloco
            inicio_bloco = min(bloco)
            fim_bloco = max(bloco)
            duracao_real = fim_bloco - inicio_bloco

            # Aplicar tempo mínimo de 1 hora
            duracao_aplicada = max(duracao_real, timedelta(hours=1))
        
        tempo_total += duracao_aplicada
        
        # Guardar detalhes para debug/relatório
        detalhes_blocos.append({
            'bloco': i + 1,
            'inicio': min(bloco).strftime('%H:%M'),
            'fim': max(bloco).strftime('%H:%M'),
            'emails_no_bloco': len(bloco),
            'duracao_real': duracao_real,
            'duracao_aplicada': duracao_aplicada
        })
    
    return tempo_total, detalhes_blocos

def formatar_tempo(tempo_delta):
    """Formata timedelta em horas e minutos"""
    if tempo_delta.total_seconds() == 0:
        return "0h 0m"
    
    total_seconds = int(tempo_delta.total_seconds())
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    
    return f"{hours}h {minutes}m"

def testar_cenarios():
    """Testa diferentes cenários de horários"""
    
    print("=" * 80)
    print("TESTE DA NOVA LÓGICA DE CÁLCULO DE TEMPO DE TRABALHO")
    print("Gap máximo: 1h | Tempo mínimo por bloco: 1h")
    print("=" * 80)
    
    # Cenário 1: Trabalho contínuo
    print("\n📋 CENÁRIO 1: Trabalho contínuo (sem gaps grandes)")
    emails1 = [
        datetime(2025, 1, 15, 9, 0),   # 09:00
        datetime(2025, 1, 15, 9, 30),  # 09:30
        datetime(2025, 1, 15, 10, 0),  # 10:00
        datetime(2025, 1, 15, 10, 45), # 10:45
        datetime(2025, 1, 15, 11, 30), # 11:30
    ]
    
    tempo1, blocos1 = calcular_tempo_trabalho_teste(emails1)
    print(f"Emails: {[e.strftime('%H:%M') for e in emails1]}")
    print(f"Tempo total: {formatar_tempo(tempo1)}")
    for bloco in blocos1:
        print(f"  Bloco {bloco['bloco']}: {bloco['inicio']}-{bloco['fim']} "
              f"({bloco['emails_no_bloco']} emails) → {formatar_tempo(bloco['duracao_aplicada'])}")
    
    # Cenário 2: Com pausa longa
    print("\n📋 CENÁRIO 2: Com pausa para almoço (gap > 1h15min)")
    emails2 = [
        datetime(2025, 1, 15, 9, 0),   # 09:00
        datetime(2025, 1, 15, 9, 45),  # 09:45
        datetime(2025, 1, 15, 10, 30), # 10:30
        datetime(2025, 1, 15, 13, 0),  # 13:00 (pausa de 2h30min)
        datetime(2025, 1, 15, 13, 30), # 13:30
        datetime(2025, 1, 15, 14, 15), # 14:15
    ]
    
    tempo2, blocos2 = calcular_tempo_trabalho_teste(emails2)
    print(f"Emails: {[e.strftime('%H:%M') for e in emails2]}")
    print(f"Tempo total: {formatar_tempo(tempo2)}")
    for bloco in blocos2:
        print(f"  Bloco {bloco['bloco']}: {bloco['inicio']}-{bloco['fim']} "
              f"({bloco['emails_no_bloco']} emails) → {formatar_tempo(bloco['duracao_aplicada'])}")
    
    # Cenário 3: Emails esparsos
    print("\n📋 CENÁRIO 3: Emails esparsos (vários blocos pequenos)")
    emails3 = [
        datetime(2025, 1, 15, 9, 0),   # 09:00 (bloco 1)
        datetime(2025, 1, 15, 11, 0),  # 11:00 (bloco 2 - gap de 2h)
        datetime(2025, 1, 15, 13, 30), # 13:30 (bloco 3 - gap de 2h30min)
        datetime(2025, 1, 15, 15, 0),  # 15:00 (bloco 4 - gap de 1h30min)
    ]
    
    tempo3, blocos3 = calcular_tempo_trabalho_teste(emails3)
    print(f"Emails: {[e.strftime('%H:%M') for e in emails3]}")
    print(f"Tempo total: {formatar_tempo(tempo3)}")
    for bloco in blocos3:
        print(f"  Bloco {bloco['bloco']}: {bloco['inicio']}-{bloco['fim']} "
              f"({bloco['emails_no_bloco']} emails) → {formatar_tempo(bloco['duracao_aplicada'])}")
    
    # Cenário 4: Gap no limite (1h)
    print("\n📋 CENÁRIO 4: Gap exatamente no limite (1h)")
    emails4 = [
        datetime(2025, 1, 15, 9, 0),   # 09:00
        datetime(2025, 1, 15, 9, 30),  # 09:30
        datetime(2025, 1, 15, 10, 30), # 10:30 (gap de 1h - ainda no mesmo bloco)
        datetime(2025, 1, 15, 11, 31), # 11:31 (gap de 1h01min - novo bloco)
    ]
    
    tempo4, blocos4 = calcular_tempo_trabalho_teste(emails4)
    print(f"Emails: {[e.strftime('%H:%M') for e in emails4]}")
    print(f"Tempo total: {formatar_tempo(tempo4)}")
    for bloco in blocos4:
        print(f"  Bloco {bloco['bloco']}: {bloco['inicio']}-{bloco['fim']} "
              f"({bloco['emails_no_bloco']} emails) → {formatar_tempo(bloco['duracao_aplicada'])}")
    
    print("\n" + "=" * 80)
    print("RESUMO DOS TESTES:")
    print(f"Cenário 1 (contínuo): {formatar_tempo(tempo1)}")
    print(f"Cenário 2 (com pausa): {formatar_tempo(tempo2)}")
    print(f"Cenário 3 (esparso): {formatar_tempo(tempo3)}")
    print(f"Cenário 4 (limite): {formatar_tempo(tempo4)}")
    print("=" * 80)

if __name__ == "__main__":
    testar_cenarios()
