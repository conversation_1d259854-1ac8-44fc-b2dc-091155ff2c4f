import csv
import requests
import json
import os
from datetime import datetime

# Token de acesso à API do Zoho Desk
ACCESS_TOKEN = "**********************************************************************"

HEADERS = {
    "Authorization": f"Zoho-oauthtoken {ACCESS_TOKEN}",
    "Content-Type": "application/json"
}

BASE_URL = "https://desk.zoho.com/api/v1"

# Período desejado - 04/04/2025 até 06/06/2025
START_DATE = datetime(2025, 4, 4)
END_DATE = datetime(2025, 6, 6, 23, 59, 59)

OUTPUT_FILE = "emails_enviados_historico_completo.csv"

def get_all_tickets():
    """Busca todos os tickets"""
    tickets = []
    from_index = 1
    limit = 100

    print("Buscando tickets...")

    while True:
        try:
            params = {"from": from_index, "limit": limit}
            response = requests.get(f"{BASE_URL}/tickets", headers=HEADERS, params=params, timeout=30)

            if response.status_code != 200:
                print(f"Erro {response.status_code}: {response.text[:200]}")
                break

            data = response.json().get("data", [])
            if not data:
                break

            tickets.extend(data)
            if len(tickets) % 500 == 0:  # Log a cada 500 tickets
                print(f"Coletados {len(tickets)} tickets...")

            from_index += limit

        except Exception as e:
            print(f"Erro: {e}")
            break

    print(f"Total: {len(tickets)} tickets encontrados")
    return tickets

def get_threads(ticket_id):
    """Busca threads de um ticket"""
    try:
        response = requests.get(f"{BASE_URL}/tickets/{ticket_id}/threads", headers=HEADERS, timeout=30)

        if response.status_code == 200:
            return response.json().get("data", [])
        else:
            return []
    except:
        return []

# Executar coleta
print("=" * 60)
print("INICIANDO COLETA DE DADOS DO ZOHO DESK")
print("=" * 60)
print(f"Período: {START_DATE.strftime('%d/%m/%Y')} até {END_DATE.strftime('%d/%m/%Y')}")
print(f"Arquivo: {OUTPUT_FILE}")
print("=" * 60)

# Buscar tickets
tickets = get_all_tickets()
total_tickets = len(tickets)

if total_tickets == 0:
    print("ERRO: Nenhum ticket encontrado!")
    exit(1)

print(f"TOTAL: {total_tickets} tickets para processar")

# Processar tickets
report = []
emails_processados = 0

for i, ticket in enumerate(tickets, 1):
    # Log de progresso mais frequente
    if i % 25 == 0 or i == 1:
        print(f"Processando {i}/{total_tickets} ({(i/total_tickets)*100:.1f}%) - {len(report)} emails encontrados")

    ticket_id = ticket["id"]
    ticket_number = ticket["ticketNumber"]
    subject = ticket.get("subject", "Sem assunto")

    threads = get_threads(ticket_id)

    for thread in threads:
        is_email = thread.get("channel") == "EMAIL"
        is_outgoing = thread.get("direction") == "out"

        if is_email and is_outgoing:
            try:
                sent_time = None
                if "createdTime" in thread:
                    try:
                        sent_time = datetime.strptime(thread["createdTime"], "%Y-%m-%dT%H:%M:%S.%fZ")
                    except:
                        try:
                            sent_time = datetime.strptime(thread["createdTime"], "%Y-%m-%dT%H:%M:%SZ")
                        except:
                            continue

                if sent_time and START_DATE <= sent_time <= END_DATE:
                    agent_name = "Desconhecido"
                    if "author" in thread and isinstance(thread["author"], dict) and "name" in thread["author"]:
                        agent_name = thread["author"]["name"]

                    report.append({
                        "ticket_number": ticket_number,
                        "subject": subject,
                        "agent": agent_name,
                        "sent_at": sent_time.strftime("%Y-%m-%d %H:%M:%S")
                    })
                    emails_processados += 1

                    # Log quando encontra emails
                    if emails_processados % 10 == 0:
                        print(f"    -> {emails_processados} emails coletados...")

            except Exception as e:
                continue

    # Salvar progresso a cada 100 tickets
    if i % 100 == 0:
        with open(OUTPUT_FILE, "w", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=["ticket_number", "subject", "agent", "sent_at"])
            writer.writeheader()
            writer.writerows(report)
        print(f"[SAVE] Progresso salvo: {len(report)} emails")

# Salvar resultado final
with open(OUTPUT_FILE, "w", newline="", encoding="utf-8") as f:
    writer = csv.DictWriter(f, fieldnames=["ticket_number", "subject", "agent", "sent_at"])
    writer.writeheader()
    writer.writerows(report)

print("=" * 60)
print("COLETA FINALIZADA!")
print(f"Total de emails: {len(report)}")
print(f"Arquivo: {OUTPUT_FILE}")
print("=" * 60)
